# Design Document

## Overview

The System Configuration Management feature will create a comprehensive web-based interface for managing all system configurations. This design leverages the existing FastAPI backend architecture and SQLAlchemy ORM to create a flexible, database-driven configuration system that can replace environment variables and configuration files with a user-friendly interface.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Web UI] --> B[Configuration API]
    B --> C[Configuration Service]
    C --> D[Configuration Models]
    D --> E[SQLite Database]
    
    F[Task System] --> G[Configuration Provider]
    H[Celery Workers] --> G
    I[External Services] --> G
    
    G --> C
    C --> J[Configuration Cache]
    C --> K[Configuration Validator]
    C --> L[Configuration History]
```

### Component Interaction

1. **Web UI**: React-based configuration interface for super administrators
2. **Configuration API**: RESTful endpoints for CRUD operations on configurations
3. **Configuration Service**: Business logic layer handling validation, caching, and history
4. **Configuration Provider**: Centralized service that provides configuration values to all system components
5. **Configuration Models**: SQLAlchemy models for storing different types of configurations
6. **Configuration Cache**: Redis-based caching for frequently accessed configurations
7. **Configuration Validator**: Validation logic for different configuration types
8. **Configuration History**: Audit trail and versioning system

## Components and Interfaces

### 1. Database Models

#### ConfigurationCategory Model
```python
class ConfigurationCategory(Base):
    __tablename__ = "configuration_categories"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)  # task, infrastructure, ai_models, system, security
    display_name = Column(String(200), nullable=False)
    description = Column(Text)
    icon = Column(String(50))  # UI icon identifier
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

#### ConfigurationItem Model
```python
class ConfigurationItem(Base):
    __tablename__ = "configuration_items"
    
    id = Column(Integer, primary_key=True)
    category_id = Column(Integer, ForeignKey("configuration_categories.id"), nullable=False)
    key = Column(String(200), unique=True, nullable=False)  # e.g., "redis.url", "task.max_concurrent"
    display_name = Column(String(200), nullable=False)
    description = Column(Text)
    value_type = Column(String(50), nullable=False)  # string, integer, float, boolean, json, list
    default_value = Column(Text)
    current_value = Column(Text)
    is_required = Column(Boolean, default=True)
    is_sensitive = Column(Boolean, default=False)  # for passwords, tokens
    validation_rules = Column(JSON)  # validation constraints
    ui_component = Column(String(50))  # input, textarea, select, toggle, file_path
    ui_options = Column(JSON)  # component-specific options
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    requires_restart = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

#### ConfigurationHistory Model
```python
class ConfigurationHistory(Base):
    __tablename__ = "configuration_history"
    
    id = Column(Integer, primary_key=True)
    configuration_item_id = Column(Integer, ForeignKey("configuration_items.id"), nullable=False)
    old_value = Column(Text)
    new_value = Column(Text)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    change_reason = Column(Text)
    change_type = Column(String(50))  # create, update, delete, import, reset
    created_at = Column(DateTime(timezone=True), server_default=func.now())
```

#### TaskConfiguration Model
```python
class TaskConfiguration(Base):
    __tablename__ = "task_configurations"
    
    id = Column(Integer, primary_key=True)
    task_type = Column(String(100), nullable=False)  # video_analysis, minicpm_analysis, etc.
    name = Column(String(200), nullable=False)
    description = Column(Text)
    parameters = Column(JSON, nullable=False)  # task-specific parameters
    prompts = Column(JSON)  # AI prompts for different scenarios
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

#### WorkerConfiguration Model
```python
class WorkerConfiguration(Base):
    __tablename__ = "worker_configurations"
    
    id = Column(Integer, primary_key=True)
    worker_name = Column(String(200), unique=True, nullable=False)
    worker_type = Column(String(100), nullable=False)  # celery, custom
    connection_params = Column(JSON, nullable=False)
    max_concurrency = Column(Integer, default=1)
    queue_names = Column(JSON)  # list of queue names
    routing_key = Column(String(200))
    prefetch_multiplier = Column(Integer, default=1)
    is_active = Column(Boolean, default=True)
    health_check_url = Column(String(500))
    last_health_check = Column(DateTime(timezone=True))
    status = Column(String(50), default="unknown")  # online, offline, error
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

### 2. Configuration Service Layer

#### ConfigurationService
```python
class ConfigurationService:
    def __init__(self, db: Session, cache: Redis):
        self.db = db
        self.cache = cache
        self.validator = ConfigurationValidator()
    
    async def get_configuration(self, key: str) -> Any
    async def set_configuration(self, key: str, value: Any, user_id: int) -> bool
    async def get_category_configurations(self, category: str) -> Dict
    async def validate_configuration(self, key: str, value: Any) -> ValidationResult
    async def test_connection(self, config_type: str, params: Dict) -> ConnectionTestResult
    async def export_configurations(self, categories: List[str] = None) -> Dict
    async def import_configurations(self, config_data: Dict, user_id: int) -> ImportResult
    async def get_configuration_history(self, key: str) -> List[ConfigurationHistory]
    async def rollback_configuration(self, history_id: int, user_id: int) -> bool
```

#### ConfigurationProvider
```python
class ConfigurationProvider:
    """Centralized configuration provider for all system components"""
    
    def __init__(self):
        self._cache = {}
        self._fallback_settings = Settings()  # existing pydantic settings
    
    def get(self, key: str, default: Any = None) -> Any
    def get_task_config(self, task_type: str) -> Dict
    def get_worker_config(self, worker_name: str) -> Dict
    def get_ai_model_config(self, model_name: str) -> Dict
    def refresh_cache(self) -> None
    def is_database_config_available(self) -> bool
```

### 3. API Endpoints

#### Configuration Management API
```python
# /api/v1/admin/configurations
GET    /categories                    # Get all configuration categories
GET    /categories/{category_id}      # Get configurations for a category
PUT    /categories/{category_id}      # Update category configurations
POST   /test-connection              # Test connection settings
GET    /history/{config_id}          # Get configuration change history
POST   /rollback/{history_id}        # Rollback to previous configuration
POST   /export                       # Export configurations
POST   /import                       # Import configurations
POST   /reset/{config_id}            # Reset to default value

# Task Configuration API
GET    /tasks/types                  # Get available task types
GET    /tasks/{task_type}/config     # Get task configuration
PUT    /tasks/{task_type}/config     # Update task configuration
POST   /tasks/{task_type}/test       # Test task configuration

# Worker Management API
GET    /workers                      # Get all workers
GET    /workers/{worker_id}          # Get worker details
PUT    /workers/{worker_id}          # Update worker configuration
POST   /workers/{worker_id}/restart  # Restart worker
POST   /workers/{worker_id}/pause    # Pause worker
POST   /workers/{worker_id}/resume   # Resume worker
GET    /workers/{worker_id}/logs     # Get worker logs
```

### 4. Frontend Components

#### Configuration Dashboard
- Category navigation sidebar
- Configuration form components
- Real-time validation feedback
- Connection test results
- Import/Export functionality

#### Task Configuration Manager
- Task type selector
- Parameter configuration forms
- Prompt editor with syntax highlighting
- Configuration templates
- Test execution interface

#### Worker Management Console
- Worker status dashboard
- Performance metrics visualization
- Log viewer
- Control buttons (start/stop/restart)
- Queue monitoring

## Data Models

### Configuration Categories Structure
```json
{
  "task": {
    "display_name": "Task Configuration",
    "description": "Settings for video analysis and processing tasks",
    "items": [
      {
        "key": "task.max_concurrent",
        "display_name": "Maximum Concurrent Tasks",
        "value_type": "integer",
        "validation_rules": {"min": 1, "max": 10}
      }
    ]
  },
  "infrastructure": {
    "display_name": "Infrastructure Settings",
    "description": "Database, Redis, and external service connections",
    "items": [
      {
        "key": "redis.url",
        "display_name": "Redis Connection URL",
        "value_type": "string",
        "is_sensitive": false,
        "validation_rules": {"pattern": "^redis://.*"}
      }
    ]
  }
}
```

### Task Configuration Schema
```json
{
  "task_type": "minicpm_analysis",
  "parameters": {
    "model_name": "openbmb/MiniCPM-V-4",
    "device": "cuda",
    "max_frames": 10,
    "sampling_interval": 5,
    "temperature": 0.7,
    "max_tokens": 2048
  },
  "prompts": {
    "scene_analysis": "Analyze this video frame and describe...",
    "content_summary": "Provide a summary of the video content...",
    "dialogue_detection": "Identify any dialogue or text in this frame..."
  }
}
```

## Error Handling

### Validation Errors
- Field-level validation with immediate feedback
- Cross-field validation for dependent configurations
- Connection testing with detailed error messages
- Configuration conflict detection

### Fallback Mechanisms
- Graceful degradation to environment variables
- Default value fallbacks
- Service availability checks
- Configuration corruption recovery

### Error Recovery
- Automatic rollback on critical failures
- Configuration backup and restore
- Service restart coordination
- Health check monitoring

## Testing Strategy

### Unit Tests
- Configuration service methods
- Validation logic
- Data model operations
- API endpoint functionality

### Integration Tests
- Database configuration persistence
- Cache synchronization
- External service connections
- Configuration migration scenarios

### End-to-End Tests
- Complete configuration workflows
- Import/export functionality
- Multi-user configuration changes
- System restart scenarios

### Performance Tests
- Configuration loading performance
- Cache efficiency
- Concurrent configuration updates
- Large configuration dataset handling

## Security Considerations

### Access Control
- Super administrator role requirement
- Permission-based configuration access
- Audit logging for all changes
- Session-based authentication

### Data Protection
- Sensitive configuration encryption
- Secure configuration transmission
- Configuration backup encryption
- Access attempt monitoring

### Validation Security
- Input sanitization
- SQL injection prevention
- Configuration tampering detection
- Malicious import prevention

## Migration Strategy

### Phase 1: Database Schema Creation
- Create configuration tables
- Populate default configurations
- Implement configuration provider

### Phase 2: Service Integration
- Update existing services to use ConfigurationProvider
- Maintain backward compatibility with environment variables
- Add configuration caching

### Phase 3: UI Development
- Build configuration management interface
- Implement real-time validation
- Add import/export functionality

### Phase 4: Advanced Features
- Worker management console
- Configuration templates
- Advanced validation rules
- Performance monitoring integration