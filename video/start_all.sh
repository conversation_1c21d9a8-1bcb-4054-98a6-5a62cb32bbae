#!/bin/bash

# 一键启动视频处理系统的所有服务
# 包括：前端开发服务器、后端API服务器、Celery工作进程

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 颜色输出函数
print_info() {
    echo -e "\033[32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

# 检查进程是否运行
check_process() {
    local name=$1
    local pid_file=$2

    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 进程正在运行
        else
            rm -f "$pid_file"  # 清理无效的PID文件
            return 1  # 进程未运行
        fi
    fi
    return 1  # PID文件不存在
}

# 检查服务是否真正在运行（通过进程名和端口）
check_service_running() {
    local service=$1

    case $service in
        "frontend")
            # 检查是否有 vite 进程和端口监听
            (pgrep -f "npm.*run.*dev" > /dev/null 2>&1 || pgrep -f "vite.*--host" > /dev/null 2>&1) && \
            (netstat -tuln | grep -E ":(3000|3001|3002) " > /dev/null 2>&1)
            return $?
            ;;
        "backend")
            # 检查是否有 uvicorn 进程，使用更宽泛的匹配
            pgrep -f "uvicorn.*main:app" > /dev/null 2>&1 || pgrep -f "python.*uvicorn" > /dev/null 2>&1
            return $?
            ;;
        "celery")
            # 检查是否有 celery worker 进程
            pgrep -f "celery.*worker" > /dev/null 2>&1
            return $?
            ;;
    esac
    return 1
}

# 获取服务的实际PID
get_service_pid() {
    local service=$1

    case $service in
        "frontend")
            # 优先查找vite进程，如果没有再查找npm进程
            pgrep -f "vite.*--host" | head -1 || pgrep -f "npm.*run.*dev" | head -1
            ;;
        "backend")
            # 查找uvicorn进程，更宽泛的匹配
            pgrep -f "uvicorn.*main:app" | head -1 || pgrep -f "python.*uvicorn" | head -1
            ;;
        "celery")
            # 查找celery worker进程
            pgrep -f "celery.*worker" | head -1
            ;;
    esac
}

# 获取服务的所有相关PID
get_all_service_pids() {
    local service=$1

    case $service in
        "frontend")
            pgrep -f "npm.*run.*dev" 2>/dev/null || pgrep -f "vite.*--host" 2>/dev/null
            ;;
        "backend")
            pgrep -f "uvicorn.*main:app" 2>/dev/null || pgrep -f "python.*uvicorn" 2>/dev/null
            ;;
        "celery")
            pgrep -f "celery.*worker" 2>/dev/null
            ;;
    esac
}

# 启动前端服务
start_frontend() {
    print_info "启动前端服务..."

    cd "$SCRIPT_DIR/frontend"

    # 检查是否已经在运行
    if check_service_running "frontend"; then
        print_warning "前端服务已经在运行中"
        local actual_pid=$(get_service_pid "frontend")
        if [ -n "$actual_pid" ]; then
            echo $actual_pid > "$LOG_DIR/frontend.pid"
            # 获取当前使用的端口
            local port=$(netstat -tlnp 2>/dev/null | grep -E ":(3000|3001|3002) " | awk '{print $4}' | cut -d':' -f2 | head -1)
            if [ -n "$port" ]; then
                print_info "前端服务运行在端口: $port"
                echo "FRONTEND_PORT=$port" > "$LOG_DIR/frontend.env"
            fi
        fi
        return
    fi

    # 检查默认端口（3000）是否被占用
    for port in 3000 3001 3002; do
        if ! netstat -tuln | grep ":$port " > /dev/null; then
            print_info "使用端口 $port"
            echo "FRONTEND_PORT=$port" > "$LOG_DIR/frontend.env"
            export VITE_PORT=$port
            break
        fi
    done

    # 后台启动前端服务
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 启动前端服务..." >> "$LOG_DIR/frontend.log"
    nohup bash start.sh > "$LOG_DIR/frontend.log" 2>&1 &
    local shell_pid=$!

    # 等待实际服务启动并获取真实PID
    sleep 3
    local actual_pid=$(get_service_pid "frontend")
    if [ -n "$actual_pid" ]; then
        echo $actual_pid > "$LOG_DIR/frontend.pid"
        print_info "前端服务已启动，PID: $actual_pid (shell PID: $shell_pid)"
    else
        echo $shell_pid > "$LOG_DIR/frontend.pid"
        print_info "前端服务已启动，Shell PID: $shell_pid"
    fi
    print_info "日志文件: $LOG_DIR/frontend.log"
}

# 启动后端API服务
start_backend() {
    print_info "启动后端API服务..."

    cd "$SCRIPT_DIR/backend"

    # 检查是否已经在运行
    if check_service_running "backend"; then
        print_warning "后端API服务已经在运行中"
        local actual_pid=$(get_service_pid "backend")
        if [ -n "$actual_pid" ]; then
            echo $actual_pid > "$LOG_DIR/backend.pid"
        fi
        return
    fi

    # 检查8000端口（默认uvicorn端口）是否被占用
    if netstat -tuln | grep ":8000 " > /dev/null; then
        print_warning "端口8000已被占用，尝试清理..."
        # 查找并终止占用8000端口的进程
        local port_pid=$(lsof -t -i:8000)
        if [ -n "$port_pid" ]; then
            print_info "终止占用端口的进程 (PID: $port_pid)..."
            kill -9 $port_pid
            sleep 2
        fi
    fi

    # 后台启动后端服务
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 启动后端服务..." >> "$LOG_DIR/backend.log"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 执行命令: bash main.sh" >> "$LOG_DIR/backend.log"

    # 启动服务并记录shell PID
    nohup bash main.sh >> "$LOG_DIR/backend.log" 2>&1 &
    local shell_pid=$!
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Shell PID: $shell_pid" >> "$LOG_DIR/backend.log"

    # 等待实际服务启动并获取真实PID
    print_info "等待后端服务启动..."
    local actual_pid=""
    local retry_count=0
    local max_retries=10

    while [ $retry_count -lt $max_retries ]; do
        sleep 1
        actual_pid=$(get_service_pid "backend")
        if [ -n "$actual_pid" ]; then
            break
        fi
        retry_count=$((retry_count + 1))
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 等待服务启动，重试 $retry_count/$max_retries" >> "$LOG_DIR/backend.log"
    done

    if [ -n "$actual_pid" ]; then
        echo $actual_pid > "$LOG_DIR/backend.pid"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 后端服务启动成功，实际PID: $actual_pid" >> "$LOG_DIR/backend.log"
        print_info "后端API服务已启动，PID: $actual_pid (shell PID: $shell_pid)"
    else
        # 如果找不到实际PID，检查shell进程是否还在运行
        if kill -0 $shell_pid 2>/dev/null; then
            echo $shell_pid > "$LOG_DIR/backend.pid"
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 使用Shell PID: $shell_pid" >> "$LOG_DIR/backend.log"
            print_warning "无法获取后端服务的实际PID，使用Shell PID: $shell_pid"
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 后端服务启动失败" >> "$LOG_DIR/backend.log"
            print_error "后端服务启动失败"
            return 1
        fi
    fi
    print_info "日志文件: $LOG_DIR/backend.log"
}

# 启动Celery工作进程
start_celery() {
    print_info "启动Celery工作进程..."

    cd "$SCRIPT_DIR/backend"

    # 检查是否已经在运行
    if check_service_running "celery"; then
        print_warning "Celery工作进程已经在运行中"
        local actual_pid=$(get_service_pid "celery")
        if [ -n "$actual_pid" ]; then
            echo $actual_pid > "$LOG_DIR/celery.pid"
        fi
        return
    fi

    # 后台启动Celery工作进程
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 启动Celery工作进程..." >> "$LOG_DIR/celery.log"
    nohup bash celery_worker.sh >> "$LOG_DIR/celery.log" 2>&1 &
    local shell_pid=$!

    # 等待实际服务启动并获取真实PID
    sleep 3
    local actual_pid=$(get_service_pid "celery")

    if [ -n "$actual_pid" ]; then
        echo $actual_pid > "$LOG_DIR/celery.pid"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Celery工作进程启动成功，PID: $actual_pid" >> "$LOG_DIR/celery.log"
        print_info "Celery工作进程已启动，PID: $actual_pid (shell PID: $shell_pid)"
    else
        echo $shell_pid > "$LOG_DIR/celery.pid"
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Celery工作进程启动，Shell PID: $shell_pid" >> "$LOG_DIR/celery.log"
        print_info "Celery工作进程已启动，Shell PID: $shell_pid"
    fi
    print_info "日志文件: $LOG_DIR/celery.log"
}

# 停止单个服务
stop_service() {
    local service=$1
    local service_name=$2

    # 首先尝试通过PID文件停止
    if [ -f "$LOG_DIR/${service}.pid" ]; then
        local pid=$(cat "$LOG_DIR/${service}.pid")
        if kill -0 "$pid" 2>/dev/null; then
            kill $pid 2>/dev/null
            sleep 2
            # 如果进程仍在运行，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 $pid 2>/dev/null
            fi
        fi
        rm -f "$LOG_DIR/${service}.pid"
    fi

    # 然后通过进程名停止所有相关进程
    case $service in
        "frontend")
            pkill -f "npm run dev" 2>/dev/null
            ;;
        "backend")
            pkill -f "uvicorn.*app.main:app" 2>/dev/null
            ;;
        "celery")
            pkill -f "celery.*worker.*video_analysis" 2>/dev/null
            ;;
    esac

    print_info "${service_name}已停止"
}

# 停止所有服务
stop_all() {
    print_info "停止所有服务..."

    stop_service "frontend" "前端服务"
    stop_service "backend" "后端API服务"
    stop_service "celery" "Celery工作进程"
}

# 查看服务状态
status() {
    print_info "检查服务状态..."

    echo "----------------------------------------"

    # 检查前端服务
    if check_service_running "frontend"; then
        local actual_pid=$(get_service_pid "frontend")
        local pid_file_pid=""
        if [ -f "$LOG_DIR/frontend.pid" ]; then
            pid_file_pid=$(cat "$LOG_DIR/frontend.pid")
        fi

        if [ -n "$actual_pid" ]; then
            echo "前端服务: 运行中 (PID: $actual_pid)"
            # 更新PID文件
            echo $actual_pid > "$LOG_DIR/frontend.pid"
        else
            echo "前端服务: 未运行"
            rm -f "$LOG_DIR/frontend.pid"
        fi
    else
        echo "前端服务: 未运行"
        rm -f "$LOG_DIR/frontend.pid"
    fi

    # 检查后端服务
    if check_service_running "backend"; then
        local actual_pid=$(get_service_pid "backend")
        local all_pids=$(get_all_service_pids "backend")
        local pid_file_pid=""
        if [ -f "$LOG_DIR/backend.pid" ]; then
            pid_file_pid=$(cat "$LOG_DIR/backend.pid")
        fi

        if [ -n "$actual_pid" ]; then
            echo "后端API服务: 运行中 (PID: $actual_pid)"
            if [ -n "$all_pids" ] && [ "$all_pids" != "$actual_pid" ]; then
                echo "  所有相关PID: $all_pids"
            fi
            # 更新PID文件
            echo $actual_pid > "$LOG_DIR/backend.pid"
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 状态检查: 后端服务运行中，PID: $actual_pid" >> "$LOG_DIR/backend.log"
        else
            echo "后端API服务: 检测到进程但无法获取PID"
            if [ -n "$pid_file_pid" ]; then
                echo "  PID文件中的PID: $pid_file_pid"
                # 检查PID文件中的进程是否还在运行
                if kill -0 "$pid_file_pid" 2>/dev/null; then
                    echo "  PID文件中的进程仍在运行"
                else
                    echo "  PID文件中的进程已停止，清理PID文件"
                    rm -f "$LOG_DIR/backend.pid"
                fi
            fi
        fi
    else
        echo "后端API服务: 未运行"
        if [ -f "$LOG_DIR/backend.pid" ]; then
            local old_pid=$(cat "$LOG_DIR/backend.pid")
            echo "  清理旧的PID文件 (PID: $old_pid)"
            rm -f "$LOG_DIR/backend.pid"
        fi
    fi

    # 检查Celery工作进程
    if check_service_running "celery"; then
        local actual_pid=$(get_service_pid "celery")
        local pid_file_pid=""
        if [ -f "$LOG_DIR/celery.pid" ]; then
            pid_file_pid=$(cat "$LOG_DIR/celery.pid")
        fi

        if [ -n "$actual_pid" ]; then
            echo "Celery工作进程: 运行中 (PID: $actual_pid)"
            # 更新PID文件
            echo $actual_pid > "$LOG_DIR/celery.pid"
        else
            echo "Celery工作进程: 未运行"
            rm -f "$LOG_DIR/celery.pid"
        fi
    else
        echo "Celery工作进程: 未运行"
        rm -f "$LOG_DIR/celery.pid"
    fi

    echo "----------------------------------------"
}

# 查看日志
logs() {
    local service=$1
    
    case $service in
        "frontend")
            if [ -f "$LOG_DIR/frontend.log" ]; then
                tail -f "$LOG_DIR/frontend.log"
            else
                print_error "前端日志文件不存在"
            fi
            ;;
        "backend")
            if [ -f "$LOG_DIR/backend.log" ]; then
                tail -f "$LOG_DIR/backend.log"
            else
                print_error "后端日志文件不存在"
            fi
            ;;
        "celery")
            if [ -f "$LOG_DIR/celery.log" ]; then
                tail -f "$LOG_DIR/celery.log"
            else
                print_error "Celery日志文件不存在"
            fi
            ;;
        *)
            print_error "未知的服务名称: $service"
            print_info "可用的服务: frontend, backend, celery"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "视频处理系统一键启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务 (默认)"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看指定服务日志"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动所有服务"
    echo "  $0 start              # 启动所有服务"
    echo "  $0 stop               # 停止所有服务"
    echo "  $0 status             # 查看服务状态"
    echo "  $0 logs frontend      # 查看前端日志"
    echo "  $0 logs backend       # 查看后端日志"
    echo "  $0 logs celery        # 查看Celery日志"
}

# 主函数
main() {
    case ${1:-start} in
        "start")
            print_info "开始启动所有服务..."
            start_frontend
            sleep 2
            start_backend
            sleep 2
            start_celery
            echo ""
            status
            print_info "所有服务启动完成！"
            print_info "使用 '$0 status' 查看服务状态"
            print_info "使用 '$0 logs <service>' 查看服务日志"
            ;;
        "stop")
            stop_all
            ;;
        "restart")
            stop_all
            sleep 3
            print_info "重新启动所有服务..."
            start_frontend
            sleep 2
            start_backend
            sleep 2
            start_celery
            echo ""
            status
            ;;
        "status")
            status
            ;;
        "logs")
            if [ -z "$2" ]; then
                print_error "请指定要查看日志的服务名称"
                print_info "可用的服务: frontend, backend, celery"
            else
                logs "$2"
            fi
            ;;
        "debug")
            print_info "调试信息..."
            echo "----------------------------------------"
            echo "脚本目录: $SCRIPT_DIR"
            echo "日志目录: $LOG_DIR"
            echo "当前目录: $(pwd)"
            echo "----------------------------------------"
            echo "目录结构检查:"
            echo "前端目录存在: $([ -d "$SCRIPT_DIR/frontend" ] && echo "是" || echo "否")"
            echo "后端目录存在: $([ -d "$SCRIPT_DIR/backend" ] && echo "是" || echo "否")"
            echo "前端启动脚本存在: $([ -f "$SCRIPT_DIR/frontend/start.sh" ] && echo "是" || echo "否")"
            echo "后端启动脚本存在: $([ -f "$SCRIPT_DIR/backend/main.sh" ] && echo "是" || echo "否")"
            echo "Celery启动脚本存在: $([ -f "$SCRIPT_DIR/backend/celery_worker.sh" ] && echo "是" || echo "否")"
            echo "----------------------------------------"
            echo "进程检查:"
            echo "所有Python进程:"
            pgrep -f python | while read pid; do
                echo "  PID: $pid - $(ps -p $pid -o command= 2>/dev/null || echo '进程已结束')"
            done
            echo ""
            echo "所有uvicorn进程:"
            pgrep -f uvicorn | while read pid; do
                echo "  PID: $pid - $(ps -p $pid -o command= 2>/dev/null || echo '进程已结束')"
            done
            echo ""
            echo "所有npm进程:"
            pgrep -f npm | while read pid; do
                echo "  PID: $pid - $(ps -p $pid -o command= 2>/dev/null || echo '进程已结束')"
            done
            echo ""
            echo "所有celery进程:"
            pgrep -f celery | while read pid; do
                echo "  PID: $pid - $(ps -p $pid -o command= 2>/dev/null || echo '进程已结束')"
            done
            echo "----------------------------------------"
            echo "PID文件检查:"
            for service in frontend backend celery; do
                local pid_file="$LOG_DIR/${service}.pid"
                if [ -f "$pid_file" ]; then
                    local pid=$(cat "$pid_file")
                    echo "$service PID文件存在: $pid"
                    if kill -0 "$pid" 2>/dev/null; then
                        echo "  进程 $pid 正在运行"
                    else
                        echo "  进程 $pid 未运行"
                    fi
                else
                    echo "$service PID文件不存在"
                fi
            done
            echo "----------------------------------------"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
