"""Add configuration management system

Revision ID: 0005
Revises: 0004
Create Date: 2025-01-06 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision = '0005'
down_revision = '0004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create configuration_categories table
    op.create_table('configuration_categories',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('name', sa.String(length=100), nullable=False),
        sa.<PERSON>umn('display_name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('icon', sa.String(length=50), nullable=True),
        sa.Column('sort_order', sa.Integer(), nullable=True, default=0),
        sa.Column('is_active', sa.<PERSON>(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_configuration_categories_id'), 'configuration_categories', ['id'], unique=False)
    op.create_index(op.f('ix_configuration_categories_name'), 'configuration_categories', ['name'], unique=True)

    # Create configuration_items table
    op.create_table('configuration_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('category_id', sa.Integer(), nullable=False),
        sa.Column('key', sa.String(length=200), nullable=False),
        sa.Column('display_name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('value_type', sa.String(length=50), nullable=False),
        sa.Column('default_value', sa.Text(), nullable=True),
        sa.Column('current_value', sa.Text(), nullable=True),
        sa.Column('is_required', sa.Boolean(), nullable=True, default=True),
        sa.Column('is_sensitive', sa.Boolean(), nullable=True, default=False),
        sa.Column('validation_rules', sa.JSON(), nullable=True),
        sa.Column('ui_component', sa.String(length=50), nullable=True),
        sa.Column('ui_options', sa.JSON(), nullable=True),
        sa.Column('sort_order', sa.Integer(), nullable=True, default=0),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('requires_restart', sa.Boolean(), nullable=True, default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.ForeignKeyConstraint(['category_id'], ['configuration_categories.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_configuration_items_id'), 'configuration_items', ['id'], unique=False)
    op.create_index(op.f('ix_configuration_items_key'), 'configuration_items', ['key'], unique=True)

    # Create configuration_history table
    op.create_table('configuration_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('configuration_item_id', sa.Integer(), nullable=False),
        sa.Column('old_value', sa.Text(), nullable=True),
        sa.Column('new_value', sa.Text(), nullable=True),
        sa.Column('changed_by', sa.Integer(), nullable=False),
        sa.Column('change_reason', sa.Text(), nullable=True),
        sa.Column('change_type', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.ForeignKeyConstraint(['changed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['configuration_item_id'], ['configuration_items.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_configuration_history_id'), 'configuration_history', ['id'], unique=False)

    # Create task_configurations table
    op.create_table('task_configurations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('task_type', sa.String(length=100), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=False),
        sa.Column('prompts', sa.JSON(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=True, default=False),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_configurations_id'), 'task_configurations', ['id'], unique=False)
    op.create_index(op.f('ix_task_configurations_task_type'), 'task_configurations', ['task_type'], unique=False)

    # Create worker_configurations table
    op.create_table('worker_configurations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('worker_name', sa.String(length=200), nullable=False),
        sa.Column('worker_type', sa.String(length=100), nullable=False),
        sa.Column('connection_params', sa.JSON(), nullable=False),
        sa.Column('max_concurrency', sa.Integer(), nullable=True, default=1),
        sa.Column('queue_names', sa.JSON(), nullable=True),
        sa.Column('routing_key', sa.String(length=200), nullable=True),
        sa.Column('prefetch_multiplier', sa.Integer(), nullable=True, default=1),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('health_check_url', sa.String(length=500), nullable=True),
        sa.Column('last_health_check', sa.DateTime(timezone=True), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=True, default='unknown'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_worker_configurations_id'), 'worker_configurations', ['id'], unique=False)
    op.create_index(op.f('ix_worker_configurations_worker_name'), 'worker_configurations', ['worker_name'], unique=True)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_worker_configurations_worker_name'), table_name='worker_configurations')
    op.drop_index(op.f('ix_worker_configurations_id'), table_name='worker_configurations')
    op.drop_table('worker_configurations')
    
    op.drop_index(op.f('ix_task_configurations_task_type'), table_name='task_configurations')
    op.drop_index(op.f('ix_task_configurations_id'), table_name='task_configurations')
    op.drop_table('task_configurations')
    
    op.drop_index(op.f('ix_configuration_history_id'), table_name='configuration_history')
    op.drop_table('configuration_history')
    
    op.drop_index(op.f('ix_configuration_items_key'), table_name='configuration_items')
    op.drop_index(op.f('ix_configuration_items_id'), table_name='configuration_items')
    op.drop_table('configuration_items')
    
    op.drop_index(op.f('ix_configuration_categories_name'), table_name='configuration_categories')
    op.drop_index(op.f('ix_configuration_categories_id'), table_name='configuration_categories')
    op.drop_table('configuration_categories')