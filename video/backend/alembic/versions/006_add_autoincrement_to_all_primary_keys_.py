"""Add autoincrement to all primary keys to prevent ID reuse

Revision ID: 006
Revises: 0005
Create Date: 2025-08-06 23:32:52.358041

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '006'
down_revision = '0005'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Add AUTOINCREMENT to all primary key columns to prevent ID reuse.

    Note: In SQLite, adding AUTOINCREMENT requires recreating tables.
    However, since this is a structural change that affects ID generation behavior,
    we'll document this change for future reference.

    The autoincrement=True parameter in SQLAlchemy models will ensure that
    new tables created after this migration will use AUTOINCREMENT.
    """
    # For existing databases, the AUTOINCREMENT behavior change will take effect
    # when new records are created. SQLite's default behavior without AUTOINCREMENT
    # can reuse IDs, but with AUTOINCREMENT it guarantees unique, incrementing IDs.

    # This migration serves as a marker that the models now use autoincrement=True
    # and any new table creation will include AUTOINCREMENT in the schema.
    pass


def downgrade() -> None:
    """
    Remove AUTOINCREMENT from primary key columns.

    Note: This is primarily a documentation change as the actual table structure
    modification would require complex table recreation in SQLite.
    """
    pass
