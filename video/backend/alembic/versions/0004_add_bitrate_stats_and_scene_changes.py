"""Add bitrate stats and scene changes tables

Revision ID: 0004
Revises: 0003
Create Date: 2025-01-06 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic.
revision = '0004'
down_revision = '0003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create bitrate_stats table
    op.create_table('bitrate_stats',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('video_id', sa.Integer(), nullable=False),
        sa.Column('stream_type', sa.String(length=20), nullable=False),
        sa.Column('avg_fps', sa.Float(), nullable=True),
        sa.Column('num_frames', sa.Integer(), nullable=True),
        sa.Column('avg_bitrate', sa.Float(), nullable=True),
        sa.Column('avg_bitrate_over_chunks', sa.Float(), nullable=True),
        sa.Column('max_bitrate', sa.Float(), nullable=True),
        sa.Column('min_bitrate', sa.Float(), nullable=True),
        sa.Column('max_bitrate_factor', sa.Float(), nullable=True),
        sa.Column('aggregation', sa.String(length=20), nullable=False),
        sa.Column('chunk_size', sa.Float(), nullable=True),
        sa.Column('duration', sa.Float(), nullable=True),
        sa.Column('bitrate_per_chunk', sa.JSON(), nullable=True),
        sa.Column('plot_data', sa.Text(), nullable=True),
        sa.Column('plot_width', sa.Integer(), nullable=True, default=70),
        sa.Column('plot_height', sa.Integer(), nullable=True, default=18),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bitrate_stats_id'), 'bitrate_stats', ['id'], unique=False)
    op.create_index(op.f('ix_bitrate_stats_video_id'), 'bitrate_stats', ['video_id'], unique=True)

    # Create scene_changes table
    op.create_table('scene_changes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('video_id', sa.Integer(), nullable=False),
        sa.Column('scene_number', sa.Integer(), nullable=False),
        sa.Column('start_time', sa.Float(), nullable=False),
        sa.Column('end_time', sa.Float(), nullable=True),
        sa.Column('start_frame', sa.Integer(), nullable=True),
        sa.Column('end_frame', sa.Integer(), nullable=True),
        sa.Column('confidence', sa.Float(), nullable=True),
        sa.Column('detector_type', sa.String(length=50), nullable=True),
        sa.Column('threshold', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=True),
        sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_scene_changes_id'), 'scene_changes', ['id'], unique=False)
    op.create_index(op.f('ix_scene_changes_video_id'), 'scene_changes', ['video_id'], unique=False)
    op.create_index(op.f('ix_scene_changes_start_time'), 'scene_changes', ['start_time'], unique=False)


def downgrade() -> None:
    # Drop scene_changes table
    op.drop_index(op.f('ix_scene_changes_start_time'), table_name='scene_changes')
    op.drop_index(op.f('ix_scene_changes_video_id'), table_name='scene_changes')
    op.drop_index(op.f('ix_scene_changes_id'), table_name='scene_changes')
    op.drop_table('scene_changes')
    
    # Drop bitrate_stats table
    op.drop_index(op.f('ix_bitrate_stats_video_id'), table_name='bitrate_stats')
    op.drop_index(op.f('ix_bitrate_stats_id'), table_name='bitrate_stats')
    op.drop_table('bitrate_stats')