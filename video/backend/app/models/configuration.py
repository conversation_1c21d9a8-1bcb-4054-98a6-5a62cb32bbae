"""
Configuration management data models
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, JSON, Boolean, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class ConfigurationCategory(Base):
    """Configuration category model"""
    __tablename__ = "configuration_categories"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), unique=True, nullable=False, index=True)  # task, infrastructure, ai_models, system, security
    display_name = Column(String(200), nullable=False)
    description = Column(Text)
    icon = Column(String(50))  # UI icon identifier
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    configuration_items = relationship("ConfigurationItem", back_populates="category", cascade="all, delete-orphan")


class ConfigurationItem(Base):
    """Configuration item model"""
    __tablename__ = "configuration_items"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    category_id = Column(Integer, ForeignKey("configuration_categories.id"), nullable=False)
    key = Column(String(200), unique=True, nullable=False, index=True)  # e.g., "redis.url", "task.max_concurrent"
    display_name = Column(String(200), nullable=False)
    description = Column(Text)
    value_type = Column(String(50), nullable=False)  # string, integer, float, boolean, json, list
    default_value = Column(Text)
    current_value = Column(Text)
    is_required = Column(Boolean, default=True)
    is_sensitive = Column(Boolean, default=False)  # for passwords, tokens
    validation_rules = Column(JSON)  # validation constraints
    ui_component = Column(String(50))  # input, textarea, select, toggle, file_path
    ui_options = Column(JSON)  # component-specific options
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    requires_restart = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    category = relationship("ConfigurationCategory", back_populates="configuration_items")
    history = relationship("ConfigurationHistory", back_populates="configuration_item", cascade="all, delete-orphan")


class ConfigurationHistory(Base):
    """Configuration history model for audit trail and versioning"""
    __tablename__ = "configuration_history"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    configuration_item_id = Column(Integer, ForeignKey("configuration_items.id"), nullable=False)
    old_value = Column(Text)
    new_value = Column(Text)
    changed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    change_reason = Column(Text)
    change_type = Column(String(50))  # create, update, delete, import, reset
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    configuration_item = relationship("ConfigurationItem", back_populates="history")
    user = relationship("User")


class TaskConfiguration(Base):
    """Task configuration model for task-specific parameters and prompts"""
    __tablename__ = "task_configurations"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_type = Column(String(100), nullable=False, index=True)  # video_analysis, minicpm_analysis, etc.
    name = Column(String(200), nullable=False)
    description = Column(Text)
    parameters = Column(JSON, nullable=False)  # task-specific parameters
    prompts = Column(JSON)  # AI prompts for different scenarios
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    creator = relationship("User")


class WorkerConfiguration(Base):
    """Worker configuration model for Celery worker management"""
    __tablename__ = "worker_configurations"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    worker_name = Column(String(200), unique=True, nullable=False, index=True)
    worker_type = Column(String(100), nullable=False)  # celery, custom
    connection_params = Column(JSON, nullable=False)
    max_concurrency = Column(Integer, default=1)
    queue_names = Column(JSON)  # list of queue names
    routing_key = Column(String(200))
    prefetch_multiplier = Column(Integer, default=1)
    is_active = Column(Boolean, default=True)
    health_check_url = Column(String(500))
    last_health_check = Column(DateTime(timezone=True))
    status = Column(String(50), default="unknown")  # online, offline, error
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())