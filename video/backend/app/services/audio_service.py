"""
Video analysis service for audio.
"""

import os
import json
import time
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger

from app.models.task import Video, AudioTrack, VideoFrame, BitrateStats
from app.services.ffmpeg_service import ffmpeg_service
from app.services.bitrate_stats_service import BitrateStatsService
from app.utils.file_organization import file_organizer


class AudioService:
    def __init__(self, db: Session):
        self.db = db

    def _get_audio_extension(self, codec_name: str) -> str:
        """Get appropriate file extension for audio codec"""
        codec_extensions = {
            "aac": "aac",
            "mp3": "mp3",
            "ac3": "ac3",
            "flac": "flac",
            "opus": "opus",
            "vorbis": "ogg",
            "pcm_s16le": "wav",
            "pcm_s24le": "wav",
            "pcm_s32le": "wav"
        }
        return codec_extensions.get(codec_name, "audio")    

    def extract_audio_tracks(self, video_id: int) -> List[str]:
        """Extract all audio tracks from video using organized directory structure"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        # Ensure video metadata is analyzed first to get audio track information
        audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()
        if not audio_tracks:
            logger.info(f"No audio tracks found for video {video_id}, analyzing metadata first...")
            # Import here to avoid circular imports
            from app.services.video_analysis_service import VideoAnalysisService
            analysis_service = VideoAnalysisService(self.db)
            analysis_service.analyze_video_metadata(video_id)
            audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()

        if not audio_tracks:
            logger.warning(f"No audio tracks found for video {video_id} after metadata analysis")
            return []

        # Create organized directory structure
        file_organizer.create_video_directory_structure(video_id)
        extracted_files = []

        success_count = 0
        for track in audio_tracks:
            try:
                # Get standardized audio file path
                codec = self._get_audio_extension(track.codec_name)
                output_path = file_organizer.get_audio_file_path(video_id, track.stream_index, codec)

                # Extract audio using ffmpeg
                success = ffmpeg_service.extract_audio(
                    video.file_path,
                    str(output_path),
                    track.stream_index
                )

                if success and os.path.exists(str(output_path)):
                    # Verify file was actually created and has content
                    file_size = os.path.getsize(str(output_path))
                    if file_size > 0:
                        # Update database with file path
                        track.file_path = str(output_path)
                        extracted_files.append(str(output_path))
                        success_count += 1
                        logger.info(f"Extracted audio track {track.stream_index} to {output_path} ({file_size} bytes)")
                    else:
                        logger.warning(f"Audio track {track.stream_index} extracted but file is empty")
                        # Remove empty file
                        try:
                            os.remove(str(output_path))
                        except:
                            pass
                else:
                    logger.warning(f"Failed to extract audio track {track.stream_index}")

            except Exception as e:
                logger.error(f"Error extracting audio track {track.stream_index}: {e}")

        self.db.commit()

        if success_count == 0 and len(audio_tracks) > 0:
            raise Exception(f"Failed to extract any audio tracks from video {video_id}")

        return extracted_files
