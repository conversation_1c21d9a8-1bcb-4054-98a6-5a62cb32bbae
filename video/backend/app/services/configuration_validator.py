"""
Configuration validation service
"""

import json
import re
import redis
import sqlalchemy
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from urllib.parse import urlparse
import os
import tempfile


@dataclass
class ValidationResult:
    """Validation result container"""
    is_valid: bool
    error_message: Optional[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class ConnectionTestResult:
    """Connection test result container"""
    is_connected: bool
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None
    additional_info: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_info is None:
            self.additional_info = {}


class ConfigurationValidator:
    """Configuration validation service"""
    
    def __init__(self):
        self.validation_rules = {
            'string': self._validate_string,
            'integer': self._validate_integer,
            'float': self._validate_float,
            'boolean': self._validate_boolean,
            'json': self._validate_json,
            'list': self._validate_list,
            'url': self._validate_url,
            'file_path': self._validate_file_path,
            'directory_path': self._validate_directory_path,
            'email': self._validate_email,
            'regex': self._validate_regex
        }
    
    def validate_configuration(self, value_type: str, value: Any, validation_rules: Optional[Dict] = None) -> ValidationResult:
        """
        Validate a configuration value based on its type and rules
        
        Args:
            value_type: Type of the value (string, integer, float, boolean, json, list, etc.)
            value: The value to validate
            validation_rules: Additional validation constraints
            
        Returns:
            ValidationResult object with validation status and messages
        """
        if validation_rules is None:
            validation_rules = {}
            
        try:
            # Get the appropriate validation function
            validator = self.validation_rules.get(value_type)
            if not validator:
                return ValidationResult(
                    is_valid=False,
                    error_message=f"Unknown value type: {value_type}"
                )
            
            # Perform basic type validation
            result = validator(value, validation_rules)
            
            # If basic validation passes, perform additional rule validation
            if result.is_valid:
                result = self._apply_additional_rules(value, validation_rules, result)
            
            return result
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                error_message=f"Validation error: {str(e)}"
            )
    
    def validate_cross_field_dependencies(self, configurations: Dict[str, Any], dependency_rules: List[Dict]) -> ValidationResult:
        """
        Validate cross-field dependencies between configurations
        
        Args:
            configurations: Dictionary of configuration key-value pairs
            dependency_rules: List of dependency rules to validate
            
        Returns:
            ValidationResult object
        """
        warnings = []
        
        for rule in dependency_rules:
            try:
                field = rule.get('field')
                depends_on = rule.get('depends_on')
                condition = rule.get('condition')
                required_value = rule.get('required_value')
                
                if not all([field, depends_on, condition]):
                    continue
                
                field_value = configurations.get(field)
                dependency_value = configurations.get(depends_on)
                
                # Check dependency condition
                if condition == 'equals' and dependency_value == required_value:
                    if field_value is None or field_value == '':
                        return ValidationResult(
                            is_valid=False,
                            error_message=f"Field '{field}' is required when '{depends_on}' equals '{required_value}'"
                        )
                elif condition == 'not_equals' and dependency_value != required_value:
                    if field_value is None or field_value == '':
                        return ValidationResult(
                            is_valid=False,
                            error_message=f"Field '{field}' is required when '{depends_on}' does not equal '{required_value}'"
                        )
                elif condition == 'exists' and dependency_value is not None:
                    if field_value is None or field_value == '':
                        return ValidationResult(
                            is_valid=False,
                            error_message=f"Field '{field}' is required when '{depends_on}' is set"
                        )
                        
            except Exception as e:
                warnings.append(f"Error validating dependency rule: {str(e)}")
        
        return ValidationResult(is_valid=True, warnings=warnings)
    
    def test_redis_connection(self, connection_params: Dict[str, Any]) -> ConnectionTestResult:
        """
        Test Redis connection
        
        Args:
            connection_params: Redis connection parameters
            
        Returns:
            ConnectionTestResult object
        """
        import time
        
        try:
            start_time = time.time()
            
            # Extract connection parameters
            url = connection_params.get('url', 'redis://localhost:6379/0')
            host = connection_params.get('host')
            port = connection_params.get('port', 6379)
            db = connection_params.get('db', 0)
            password = connection_params.get('password')
            
            # Create Redis client
            if url:
                client = redis.from_url(url)
            else:
                client = redis.Redis(
                    host=host or 'localhost',
                    port=port,
                    db=db,
                    password=password,
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
            
            # Test connection
            client.ping()
            
            # Get server info
            info = client.info()
            response_time = (time.time() - start_time) * 1000
            
            return ConnectionTestResult(
                is_connected=True,
                response_time_ms=response_time,
                additional_info={
                    'redis_version': info.get('redis_version'),
                    'used_memory_human': info.get('used_memory_human'),
                    'connected_clients': info.get('connected_clients')
                }
            )
            
        except redis.ConnectionError as e:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"Redis connection failed: {str(e)}"
            )
        except Exception as e:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"Redis test error: {str(e)}"
            )
    
    def test_database_connection(self, connection_params: Dict[str, Any]) -> ConnectionTestResult:
        """
        Test database connection
        
        Args:
            connection_params: Database connection parameters
            
        Returns:
            ConnectionTestResult object
        """
        import time
        
        try:
            start_time = time.time()
            
            # Extract connection parameters
            url = connection_params.get('url')
            if not url:
                return ConnectionTestResult(
                    is_connected=False,
                    error_message="Database URL is required"
                )
            
            # Create engine and test connection
            engine = sqlalchemy.create_engine(
                url,
                connect_args={"check_same_thread": False} if "sqlite" in url else {}
            )
            
            # Test connection
            with engine.connect() as conn:
                result = conn.execute(sqlalchemy.text("SELECT 1"))
                result.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            return ConnectionTestResult(
                is_connected=True,
                response_time_ms=response_time,
                additional_info={
                    'database_type': url.split('://')[0] if '://' in url else 'unknown'
                }
            )
            
        except sqlalchemy.exc.SQLAlchemyError as e:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"Database connection failed: {str(e)}"
            )
        except Exception as e:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"Database test error: {str(e)}"
            )
    
    def test_external_service_connection(self, service_type: str, connection_params: Dict[str, Any]) -> ConnectionTestResult:
        """
        Test external service connection
        
        Args:
            service_type: Type of service (http, https, ftp, etc.)
            connection_params: Service connection parameters
            
        Returns:
            ConnectionTestResult object
        """
        if service_type.lower() in ['redis']:
            return self.test_redis_connection(connection_params)
        elif service_type.lower() in ['database', 'db', 'sql']:
            return self.test_database_connection(connection_params)
        elif service_type.lower() in ['http', 'https']:
            return self._test_http_connection(connection_params)
        else:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"Unsupported service type: {service_type}"
            )
    
    def _test_http_connection(self, connection_params: Dict[str, Any]) -> ConnectionTestResult:
        """Test HTTP/HTTPS connection"""
        import httpx
        import time
        
        try:
            start_time = time.time()
            
            url = connection_params.get('url')
            timeout = connection_params.get('timeout', 10)
            headers = connection_params.get('headers', {})
            
            if not url:
                return ConnectionTestResult(
                    is_connected=False,
                    error_message="URL is required for HTTP connection test"
                )
            
            with httpx.Client(timeout=timeout) as client:
                response = client.get(url, headers=headers)
                response.raise_for_status()
            
            response_time = (time.time() - start_time) * 1000
            
            return ConnectionTestResult(
                is_connected=True,
                response_time_ms=response_time,
                additional_info={
                    'status_code': response.status_code,
                    'content_type': response.headers.get('content-type')
                }
            )
            
        except httpx.RequestError as e:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"HTTP connection failed: {str(e)}"
            )
        except Exception as e:
            return ConnectionTestResult(
                is_connected=False,
                error_message=f"HTTP test error: {str(e)}"
            )
    
    # Type-specific validation methods
    def _validate_string(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate string value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            try:
                value = str(value)
            except:
                return ValidationResult(is_valid=False, error_message="Cannot convert to string")
        
        # Check for empty string when required
        if rules.get('required', True) and value == '':
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        # Check length constraints
        min_length = rules.get('min_length')
        max_length = rules.get('max_length')
        
        if min_length is not None and len(value) < min_length:
            return ValidationResult(is_valid=False, error_message=f"String too short (minimum {min_length} characters)")
        
        if max_length is not None and len(value) > max_length:
            return ValidationResult(is_valid=False, error_message=f"String too long (maximum {max_length} characters)")
        
        # Check pattern
        pattern = rules.get('pattern')
        if pattern and not re.match(pattern, value):
            return ValidationResult(is_valid=False, error_message=f"String does not match required pattern")
        
        return ValidationResult(is_valid=True)
    
    def _validate_integer(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate integer value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        try:
            if isinstance(value, str):
                value = int(value)
            elif not isinstance(value, int):
                return ValidationResult(is_valid=False, error_message="Value must be an integer")
        except ValueError:
            return ValidationResult(is_valid=False, error_message="Cannot convert to integer")
        
        # Check range constraints
        min_value = rules.get('min')
        max_value = rules.get('max')
        
        if min_value is not None and value < min_value:
            return ValidationResult(is_valid=False, error_message=f"Value too small (minimum {min_value})")
        
        if max_value is not None and value > max_value:
            return ValidationResult(is_valid=False, error_message=f"Value too large (maximum {max_value})")
        
        return ValidationResult(is_valid=True)
    
    def _validate_float(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate float value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        try:
            if isinstance(value, str):
                value = float(value)
            elif not isinstance(value, (int, float)):
                return ValidationResult(is_valid=False, error_message="Value must be a number")
        except ValueError:
            return ValidationResult(is_valid=False, error_message="Value must be a number")
        
        # Check range constraints
        min_value = rules.get('min')
        max_value = rules.get('max')
        
        if min_value is not None and value < min_value:
            return ValidationResult(is_valid=False, error_message=f"Value too small (minimum {min_value})")
        
        if max_value is not None and value > max_value:
            return ValidationResult(is_valid=False, error_message=f"Value too large (maximum {max_value})")
        
        return ValidationResult(is_valid=True)
    
    def _validate_boolean(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate boolean value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if isinstance(value, bool):
            return ValidationResult(is_valid=True)
        
        if isinstance(value, str):
            if value.lower() in ['true', '1', 'yes', 'on']:
                return ValidationResult(is_valid=True)
            elif value.lower() in ['false', '0', 'no', 'off']:
                return ValidationResult(is_valid=True)
            else:
                return ValidationResult(is_valid=False, error_message="Invalid boolean value")
        
        if isinstance(value, (int, float)):
            return ValidationResult(is_valid=True)
        
        return ValidationResult(is_valid=False, error_message="Value must be a boolean")
    
    def _validate_json(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate JSON value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if isinstance(value, (dict, list)):
            try:
                json.dumps(value)
                return ValidationResult(is_valid=True)
            except (TypeError, ValueError) as e:
                return ValidationResult(is_valid=False, error_message=f"Invalid JSON structure: {str(e)}")
        
        if isinstance(value, str):
            try:
                json.loads(value)
                return ValidationResult(is_valid=True)
            except json.JSONDecodeError as e:
                return ValidationResult(is_valid=False, error_message=f"Invalid JSON string: {str(e)}")
        
        return ValidationResult(is_valid=False, error_message="Value must be valid JSON")
    
    def _validate_list(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate list value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if isinstance(value, str):
            try:
                value = json.loads(value)
            except json.JSONDecodeError:
                # Try comma-separated values
                value = [item.strip() for item in value.split(',')]
        
        if not isinstance(value, list):
            return ValidationResult(is_valid=False, error_message="Value must be a list")
        
        # Check length constraints
        min_length = rules.get('min_length')
        max_length = rules.get('max_length')
        
        if min_length is not None and len(value) < min_length:
            return ValidationResult(is_valid=False, error_message=f"List too short (minimum {min_length} items)")
        
        if max_length is not None and len(value) > max_length:
            return ValidationResult(is_valid=False, error_message=f"List too long (maximum {max_length} items)")
        
        return ValidationResult(is_valid=True)
    
    def _validate_url(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate URL value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, error_message="URL must be a string")
        
        try:
            parsed = urlparse(value)
            if not parsed.scheme or not parsed.netloc:
                return ValidationResult(is_valid=False, error_message="Invalid URL format")
            
            # Additional validation for netloc - should have at least a dot or be localhost
            if parsed.netloc != 'localhost' and '.' not in parsed.netloc:
                return ValidationResult(is_valid=False, error_message="Invalid URL format")
            
            # Check allowed schemes
            allowed_schemes = rules.get('allowed_schemes', ['http', 'https', 'ftp', 'redis'])
            if parsed.scheme not in allowed_schemes:
                return ValidationResult(is_valid=False, error_message=f"URL scheme must be one of: {', '.join(allowed_schemes)}")
            
            return ValidationResult(is_valid=True)
            
        except Exception as e:
            return ValidationResult(is_valid=False, error_message=f"Invalid URL: {str(e)}")
    
    def _validate_file_path(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate file path value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, error_message="File path must be a string")
        
        # Check if file exists (if required)
        if rules.get('must_exist', False):
            if not os.path.isfile(value):
                return ValidationResult(is_valid=False, error_message="File does not exist")
        
        # Check file extension
        allowed_extensions = rules.get('allowed_extensions')
        if allowed_extensions:
            _, ext = os.path.splitext(value)
            if ext.lower() not in [e.lower() for e in allowed_extensions]:
                return ValidationResult(is_valid=False, error_message=f"File extension must be one of: {', '.join(allowed_extensions)}")
        
        return ValidationResult(is_valid=True)
    
    def _validate_directory_path(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate directory path value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, error_message="Directory path must be a string")
        
        # Check if directory exists (if required)
        if rules.get('must_exist', False):
            if not os.path.isdir(value):
                return ValidationResult(is_valid=False, error_message="Directory does not exist")
        
        # Check if directory is writable (if required)
        if rules.get('must_be_writable', False):
            if os.path.exists(value) and not os.access(value, os.W_OK):
                return ValidationResult(is_valid=False, error_message="Directory is not writable")
        
        return ValidationResult(is_valid=True)
    
    def _validate_email(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate email value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, error_message="Email must be a string")
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            return ValidationResult(is_valid=False, error_message="Invalid email format")
        
        return ValidationResult(is_valid=True)
    
    def _validate_regex(self, value: Any, rules: Dict) -> ValidationResult:
        """Validate regex pattern value"""
        if value is None and rules.get('required', True):
            return ValidationResult(is_valid=False, error_message="Value is required")
        
        if value is None:
            return ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, error_message="Regex pattern must be a string")
        
        try:
            re.compile(value)
            return ValidationResult(is_valid=True)
        except re.error as e:
            return ValidationResult(is_valid=False, error_message=f"Invalid regex pattern: {str(e)}")
    
    def _apply_additional_rules(self, value: Any, rules: Dict, current_result: ValidationResult) -> ValidationResult:
        """Apply additional validation rules"""
        warnings = current_result.warnings.copy()
        
        # Check allowed values
        allowed_values = rules.get('allowed_values')
        if allowed_values and value not in allowed_values:
            return ValidationResult(
                is_valid=False,
                error_message=f"Value must be one of: {', '.join(map(str, allowed_values))}"
            )
        
        # Check forbidden values
        forbidden_values = rules.get('forbidden_values')
        if forbidden_values and value in forbidden_values:
            return ValidationResult(
                is_valid=False,
                error_message=f"Value cannot be one of: {', '.join(map(str, forbidden_values))}"
            )
        
        # Add warnings for deprecated values
        deprecated_values = rules.get('deprecated_values')
        if deprecated_values and value in deprecated_values:
            warnings.append(f"Value '{value}' is deprecated and may be removed in future versions")
        
        return ValidationResult(is_valid=True, warnings=warnings)