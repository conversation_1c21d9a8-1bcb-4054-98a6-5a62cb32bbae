"""
比特率统计服务
集成 ffmpeg-bitrate-stats 包的功能
"""

import os
import json
from typing import Dict, Optional
from sqlalchemy.orm import Session
from loguru import logger

from ffmpeg_bitrate_stats import BitrateStats as FFmpegBitrateStats
from app.models.task import Video, BitrateStats
from app.core.database import get_db


class BitrateStatsService:
    """比特率统计服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def analyze_video_bitrate(
        self, 
        video_id: int, 
        stream_type: str = "video",
        aggregation: str = "time",
        chunk_size: float = 30.0,
        plot_width: int = 70,
        plot_height: int = 18
    ) -> BitrateStats:
        """
        分析视频比特率统计
        
        Args:
            video_id: 视频ID
            stream_type: 流类型 ("video" 或 "audio")
            aggregation: 聚合方式 ("time" 或 "gop")
            chunk_size: 块大小（秒，仅当aggregation="time"时有效）
            plot_width: 图表宽度
            plot_height: 图表高度
            
        Returns:
            BitrateStats: 比特率统计对象
        """
        # 获取视频信息
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        if not os.path.exists(video.file_path):
            raise FileNotFoundError(f"Video file not found: {video.file_path}")
        
        try:
            logger.info(f"Starting bitrate analysis for video {video_id}")

            # 使用 ffmpeg-bitrate-stats 分析
            ffbs = FFmpegBitrateStats(
                input_file=video.file_path,
                stream_type=stream_type,
                aggregation=aggregation,
                chunk_size=int(chunk_size) if aggregation == "time" else 1
            )

            # 执行分析
            stats_summary = ffbs.calculate_statistics()

            # 获取JSON格式的统计结果
            stats_json = ffbs.get_json()
            stats_data = json.loads(stats_json)

            # 生成ASCII图表
            plot_data = self._generate_plot(ffbs, plot_width, plot_height)
            
            # 检查是否已存在统计数据
            existing_stats = self.db.query(BitrateStats).filter(
                BitrateStats.video_id == video_id
            ).first()
            
            if existing_stats:
                # 更新现有记录
                bitrate_stats = self._update_bitrate_stats(
                    existing_stats, stats_data, plot_data, 
                    stream_type, aggregation, chunk_size, plot_width, plot_height
                )
            else:
                # 创建新记录
                bitrate_stats = self._create_bitrate_stats(
                    video_id, stats_data, plot_data,
                    stream_type, aggregation, chunk_size, plot_width, plot_height
                )
            
            self.db.commit()
            logger.info(f"Bitrate analysis completed for video {video_id}")
            
            return bitrate_stats
            
        except Exception as e:
            logger.error(f"Failed to analyze bitrate for video {video_id}: {e}")
            self.db.rollback()
            raise
    
    def _create_bitrate_stats(
        self, 
        video_id: int, 
        stats_data: Dict, 
        plot_data: str,
        stream_type: str,
        aggregation: str,
        chunk_size: float,
        plot_width: int,
        plot_height: int
    ) -> BitrateStats:
        """创建新的比特率统计记录"""
        
        bitrate_stats = BitrateStats(
            video_id=video_id,
            stream_type=stream_type,
            avg_fps=stats_data.get("avg_fps"),
            num_frames=stats_data.get("num_frames"),
            avg_bitrate=stats_data.get("avg_bitrate"),
            avg_bitrate_over_chunks=stats_data.get("avg_bitrate_over_chunks"),
            max_bitrate=stats_data.get("max_bitrate"),
            min_bitrate=stats_data.get("min_bitrate"),
            max_bitrate_factor=stats_data.get("max_bitrate_factor"),
            aggregation=aggregation,
            chunk_size=chunk_size if aggregation == "time" else None,
            duration=stats_data.get("duration"),
            bitrate_per_chunk=stats_data.get("bitrate_per_chunk", []),
            plot_data=plot_data,
            plot_width=plot_width,
            plot_height=plot_height
        )
        
        self.db.add(bitrate_stats)
        return bitrate_stats
    
    def _update_bitrate_stats(
        self, 
        existing_stats: BitrateStats, 
        stats_data: Dict, 
        plot_data: str,
        stream_type: str,
        aggregation: str,
        chunk_size: float,
        plot_width: int,
        plot_height: int
    ) -> BitrateStats:
        """更新现有的比特率统计记录"""
        
        existing_stats.stream_type = stream_type
        existing_stats.avg_fps = stats_data.get("avg_fps")
        existing_stats.num_frames = stats_data.get("num_frames")
        existing_stats.avg_bitrate = stats_data.get("avg_bitrate")
        existing_stats.avg_bitrate_over_chunks = stats_data.get("avg_bitrate_over_chunks")
        existing_stats.max_bitrate = stats_data.get("max_bitrate")
        existing_stats.min_bitrate = stats_data.get("min_bitrate")
        existing_stats.max_bitrate_factor = stats_data.get("max_bitrate_factor")
        existing_stats.aggregation = aggregation
        existing_stats.chunk_size = chunk_size if aggregation == "time" else None
        existing_stats.duration = stats_data.get("duration")
        existing_stats.bitrate_per_chunk = stats_data.get("bitrate_per_chunk", [])
        existing_stats.plot_data = plot_data
        existing_stats.plot_width = plot_width
        existing_stats.plot_height = plot_height
        
        return existing_stats
    
    def _generate_plot(self, ffbs: FFmpegBitrateStats, width: int, height: int) -> str:
        """生成ASCII图表"""
        try:
            # 使用 ffmpeg-bitrate-stats 的内置绘图功能
            import io
            import sys
            from contextlib import redirect_stderr

            # 捕获绘图输出
            plot_output = io.StringIO()

            # 重定向stderr来捕获图表输出
            with redirect_stderr(plot_output):
                ffbs.plot(width=width, height=height)

            plot_data = plot_output.getvalue()
            return plot_data

        except Exception as e:
            logger.warning(f"Failed to generate plot: {e}")
            return f"Plot generation failed: {str(e)}"
    
    def get_bitrate_stats(self, video_id: int) -> Optional[BitrateStats]:
        """获取视频的比特率统计"""
        try:
            logger.info(f"Querying bitrate stats from database for video {video_id}")
            
            stats = self.db.query(BitrateStats).filter(
                BitrateStats.video_id == video_id
            ).first()
            
            if stats:
                logger.info(f"Found bitrate stats record for video {video_id}, id: {stats.id}")
            else:
                logger.info(f"No bitrate stats record found for video {video_id}")
                
            return stats
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Database error getting bitrate stats for video {video_id}: {e}")
            logger.error(f"Full traceback: {error_traceback}")
            raise
    
    def get_bitrate_stats_summary(self, video_id: int) -> Optional[Dict]:
        """获取比特率统计摘要"""
        try:
            logger.info(f"Getting bitrate stats summary for video {video_id}")
            
            stats = self.get_bitrate_stats(video_id)
            if not stats:
                logger.warning(f"No bitrate stats found for video {video_id}")
                return None
            
            logger.info(f"Found bitrate stats for video {video_id}, stream_type: {stats.stream_type}")
            
            summary = {
                "video_id": stats.video_id,
                "stream_type": stats.stream_type,
                "avg_fps": stats.avg_fps,
                "num_frames": stats.num_frames,
                "avg_bitrate": stats.avg_bitrate,
                "avg_bitrate_over_chunks": stats.avg_bitrate_over_chunks,
                "max_bitrate": stats.max_bitrate,
                "min_bitrate": stats.min_bitrate,
                "max_bitrate_factor": stats.max_bitrate_factor,
                "aggregation": stats.aggregation,
                "chunk_size": stats.chunk_size,
                "duration": stats.duration,
                "bitrate_per_chunk": stats.bitrate_per_chunk,
                "plot_data": stats.plot_data,
                "plot_width": stats.plot_width,
                "plot_height": stats.plot_height,
                "created_at": stats.created_at.isoformat() if stats.created_at else None,
                "updated_at": stats.updated_at.isoformat() if stats.updated_at else None
            }
            
            logger.info(f"Successfully created bitrate stats summary for video {video_id}")
            return summary
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error getting bitrate stats summary for video {video_id}: {e}")
            logger.error(f"Full traceback: {error_traceback}")
            raise
