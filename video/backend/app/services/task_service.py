"""
任务管理服务
"""

from sqlalchemy.orm import Session
from app.models.task import Task, Video
from app.core.database import get_db
from typing import List, Optional
import os
import uuid
import shutil
from datetime import datetime
from pathlib import Path
from loguru import logger


class TaskService:
    """任务管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_task(self, name: str, description: str = "", config: dict = None) -> Task:
        """创建新任务"""
        task = Task(
            name=name,
            description=description,
            config=config or {},
            status="pending",
            progress=0.0
        )
        
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        
        return task
    
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务详情"""
        return self.db.query(Task).filter(Task.id == task_id).first()
    
    def get_tasks(self, limit: int = 100, offset: int = 0) -> List[Task]:
        """获取任务列表"""
        return self.db.query(Task).offset(offset).limit(limit).all()
    
    def update_task(self, task_id: int, **updates) -> Optional[Task]:
        """更新任务"""
        task = self.get_task(task_id)
        if not task:
            return None
        
        for key, value in updates.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        task.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(task)
        
        return task
    
    def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        task = self.get_task(task_id)
        if not task:
            return False

        # 收集需要删除的video文件夹路径
        video_dirs_to_delete = []
        for video in task.videos:
            # 获取video文件夹路径
            from app.utils.file_organization import file_organizer
            video_dir = file_organizer.get_video_directory(video.id)
            if video_dir.exists():
                video_dirs_to_delete.append(video_dir)

            # 手动处理循环依赖：先清除所有video的key_frame_thumbnail_id引用
            if video.key_frame_thumbnail_id:
                video.key_frame_thumbnail_id = None

        # 提交清除引用的更改
        self.db.commit()

        # 现在可以安全删除任务（会级联删除videos和frames）
        self.db.delete(task)
        self.db.commit()

        # 删除相关的文件夹
        for video_dir in video_dirs_to_delete:
            try:
                if video_dir.exists():
                    shutil.rmtree(video_dir)
                    logger.info(f"Deleted video directory: {video_dir}")
            except Exception as e:
                logger.error(f"Failed to delete video directory {video_dir}: {e}")

        return True
    
    def update_progress(self, task_id: int, progress: float) -> Optional[Task]:
        """更新任务进度"""
        return self.update_task(task_id, progress=progress)
    
    def set_status(self, task_id: int, status: str) -> Optional[Task]:
        """设置任务状态"""
        return self.update_task(task_id, status=status)
