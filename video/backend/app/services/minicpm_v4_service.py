"""
MiniCPM-V-4 Vision Language Model Service
Provides video frame analysis and comparison capabilities using MiniCPM-V-4
"""

import os
import json
import logging
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path
from PIL import Image
import torch
from transformers import AutoModel, AutoTokenizer
from sqlalchemy.orm import Session

from app.models.task import Video, VideoFrame, AnalysisResult, Subtitle
from app.core.config import settings

logger = logging.getLogger(__name__)


class MiniCPMV4Service:
    """Service for MiniCPM-V-4 vision language model integration"""
    
    def __init__(self, db: Session):
        self.db = db
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize MiniCPM-V-4 model and tokenizer"""
        try:
            logger.info("Initializing MiniCPM-V-4 model...")
            
            # Load model with optimized settings
            self.model = AutoModel.from_pretrained(
                'openbmb/MiniCPM-V-4',
                trust_remote_code=True,
                attn_implementation='sdpa',  # or flash_attention_2
                torch_dtype=torch.bfloat16
            )
            
            self.model = self.model.eval()
            if self.device == "cuda":
                self.model = self.model.cuda()
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                'openbmb/MiniCPM-V-4',
                trust_remote_code=True
            )
            
            logger.info(f"MiniCPM-V-4 model initialized successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Failed to initialize MiniCPM-V-4 model: {e}")
            raise
    
    def analyze_single_frame(
        self, 
        video_id: int, 
        frame_path: str, 
        question: str = "Describe what you see in this image in detail."
    ) -> Dict[str, Any]:
        """
        Analyze a single video frame using MiniCPM-V-4
        
        Args:
            video_id: Video ID
            frame_path: Path to the frame image
            question: Question to ask about the image
            
        Returns:
            Dict containing analysis results
        """
        try:
            if not os.path.exists(frame_path):
                raise FileNotFoundError(f"Frame image not found: {frame_path}")
            
            # Load and process image
            image = Image.open(frame_path).convert('RGB')
            
            # Prepare messages for the model
            msgs = [{'role': 'user', 'content': [image, question]}]
            
            # Get model response
            with torch.no_grad():
                answer = self.model.chat(
                    msgs=msgs,
                    tokenizer=self.tokenizer
                )
            
            result = {
                "frame_path": frame_path,
                "question": question,
                "analysis": answer,
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            logger.info(f"Successfully analyzed frame: {frame_path}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze frame {frame_path}: {e}")
            raise
    
    def compare_frames(
        self, 
        video_id: int, 
        frame_paths: List[str], 
        question: str = "Compare these images and tell me about the differences between them."
    ) -> Dict[str, Any]:
        """
        Compare multiple video frames using MiniCPM-V-4
        
        Args:
            video_id: Video ID
            frame_paths: List of paths to frame images
            question: Question to ask about the images
            
        Returns:
            Dict containing comparison results
        """
        try:
            if len(frame_paths) < 2:
                raise ValueError("At least 2 frames are required for comparison")
            
            # Load images
            images = []
            for frame_path in frame_paths:
                if not os.path.exists(frame_path):
                    raise FileNotFoundError(f"Frame image not found: {frame_path}")
                image = Image.open(frame_path).convert('RGB')
                images.append(image)
            
            # Prepare messages with multiple images
            content = images + [question]
            msgs = [{'role': 'user', 'content': content}]
            
            # Get model response
            with torch.no_grad():
                answer = self.model.chat(
                    msgs=msgs,
                    tokenizer=self.tokenizer
                )
            
            result = {
                "frame_paths": frame_paths,
                "question": question,
                "comparison": answer,
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            logger.info(f"Successfully compared {len(frame_paths)} frames")
            return result
            
        except Exception as e:
            logger.error(f"Failed to compare frames: {e}")
            raise
    
    def analyze_video_scenes(
        self, 
        video_id: int, 
        scene_analysis_type: str = "content"
    ) -> Dict[str, Any]:
        """
        Analyze video scenes using key frames from the database
        
        Args:
            video_id: Video ID
            scene_analysis_type: Type of analysis (content, emotion, action, etc.)
            
        Returns:
            Dict containing scene analysis results
        """
        try:
            # Get video and its frames from database
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                raise ValueError(f"Video {video_id} not found")
            
            # Get key frames
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).all()
            
            if not key_frames:
                raise ValueError(f"No key frames found for video {video_id}")
            
            # Prepare analysis question based on type
            questions = {
                "content": "Describe the main content, objects, and activities in this scene.",
                "emotion": "Analyze the emotional tone and mood of this scene.",
                "action": "Describe the actions and movements happening in this scene.",
                "characters": "Identify and describe any people or characters in this scene.",
                "setting": "Describe the setting, location, and environment of this scene."
            }
            
            question = questions.get(scene_analysis_type, questions["content"])
            
            # Analyze each key frame
            scene_analyses = []
            for frame in key_frames:
                if os.path.exists(frame.file_path):
                    analysis = self.analyze_single_frame(
                        video_id, 
                        frame.file_path, 
                        question
                    )
                    analysis.update({
                        "frame_id": frame.id,
                        "frame_number": frame.frame_number,
                        "timestamp": frame.timestamp
                    })
                    scene_analyses.append(analysis)
            
            result = {
                "video_id": video_id,
                "analysis_type": scene_analysis_type,
                "total_scenes": len(scene_analyses),
                "scenes": scene_analyses,
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            # Save analysis result to database
            self._save_analysis_result(video_id, "minicpm_scene_analysis", result)
            
            logger.info(f"Successfully analyzed {len(scene_analyses)} scenes for video {video_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze video scenes for video {video_id}: {e}")
            raise
    
    def detect_scene_changes(
        self, 
        video_id: int, 
        comparison_threshold: int = 5
    ) -> Dict[str, Any]:
        """
        Detect scene changes by comparing consecutive frames
        
        Args:
            video_id: Video ID
            comparison_threshold: Number of frames to compare for change detection
            
        Returns:
            Dict containing scene change detection results
        """
        try:
            # Get key frames ordered by timestamp
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).all()
            
            if len(key_frames) < 2:
                raise ValueError(f"Need at least 2 key frames for scene change detection")
            
            scene_changes = []
            
            # Compare consecutive frames in groups
            for i in range(0, len(key_frames) - comparison_threshold, comparison_threshold):
                frame_group = key_frames[i:i + comparison_threshold + 1]
                frame_paths = [f.file_path for f in frame_group if os.path.exists(f.file_path)]
                
                if len(frame_paths) >= 2:
                    comparison_result = self.compare_frames(
                        video_id,
                        frame_paths,
                        "Analyze if there are significant scene changes between these consecutive frames. Focus on major changes in setting, lighting, camera angle, or content."
                    )
                    
                    scene_change = {
                        "start_frame": frame_group[0].frame_number,
                        "end_frame": frame_group[-1].frame_number,
                        "start_timestamp": frame_group[0].timestamp,
                        "end_timestamp": frame_group[-1].timestamp,
                        "comparison_result": comparison_result["comparison"],
                        "frame_paths": frame_paths
                    }
                    scene_changes.append(scene_change)
            
            result = {
                "video_id": video_id,
                "total_comparisons": len(scene_changes),
                "scene_changes": scene_changes,
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            # Save analysis result to database
            self._save_analysis_result(video_id, "minicpm_scene_changes", result)
            
            logger.info(f"Successfully detected scene changes for video {video_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to detect scene changes for video {video_id}: {e}")
            raise
    
    def analyze_video_content_summary(self, video_id: int) -> Dict[str, Any]:
        """
        Generate a comprehensive content summary of the video using key frames
        
        Args:
            video_id: Video ID
            
        Returns:
            Dict containing video content summary
        """
        try:
            # Get representative key frames (sample every few frames to avoid too many)
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).all()
            
            if not key_frames:
                raise ValueError(f"No key frames found for video {video_id}")
            
            # Sample frames for summary (take every 5th frame or max 10 frames)
            sample_interval = max(1, len(key_frames) // 10)
            sampled_frames = key_frames[::sample_interval][:10]
            
            frame_paths = [f.file_path for f in sampled_frames if os.path.exists(f.file_path)]
            
            if not frame_paths:
                raise ValueError(f"No valid frame paths found for video {video_id}")
            
            # Generate comprehensive summary
            summary_result = self.compare_frames(
                video_id,
                frame_paths,
                "Analyze these key frames from a video and provide a comprehensive summary including: 1) Main themes and content, 2) Visual style and aesthetics, 3) Key scenes or moments, 4) Overall narrative or progression, 5) Notable visual elements or patterns."
            )
            
            result = {
                "video_id": video_id,
                "summary": summary_result["comparison"],
                "analyzed_frames": len(frame_paths),
                "total_key_frames": len(key_frames),
                "frame_timestamps": [f.timestamp for f in sampled_frames],
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            # Save analysis result to database
            self._save_analysis_result(video_id, "minicpm_content_summary", result)
            
            logger.info(f"Successfully generated content summary for video {video_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate content summary for video {video_id}: {e}")
            raise
    
    def _save_analysis_result(self, video_id: int, step: str, result: Dict[str, Any]):
        """Save analysis result to database"""
        try:
            analysis_result = AnalysisResult(
                video_id=video_id,
                step=step,
                result=result,
                confidence=0.9,  # Default confidence for MiniCPM-V-4
                processing_time=0.0  # Will be updated if timing is tracked
            )
            
            self.db.add(analysis_result)
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Failed to save analysis result: {e}")
            self.db.rollback()
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp as ISO string"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        return {
            "model_name": "MiniCPM-V-4",
            "device": self.device,
            "torch_dtype": str(self.model.dtype) if self.model else None,
            "model_loaded": self.model is not None,
            "tokenizer_loaded": self.tokenizer is not None
        }    

    # ========== 字幕集成方法 ==========
    
    def _get_video_subtitles(self, video_id: int) -> List[Dict[str, Any]]:
        """获取视频的字幕内容"""
        try:
            subtitles = self.db.query(Subtitle).filter(
                Subtitle.video_id == video_id
            ).all()
            
            subtitle_data = []
            for subtitle in subtitles:
                if subtitle.content:
                    try:
                        content = json.loads(subtitle.content) if isinstance(subtitle.content, str) else subtitle.content
                        subtitle_data.append({
                            "id": subtitle.id,
                            "language": subtitle.language,
                            "subtitle_type": subtitle.subtitle_type,
                            "content": content,
                            "confidence": subtitle.confidence
                        })
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Failed to parse subtitle content for subtitle {subtitle.id}: {e}")
                        continue
            
            return subtitle_data
            
        except Exception as e:
            logger.error(f"Failed to get subtitles for video {video_id}: {e}")
            return []
    
    def _get_subtitle_text_for_timerange(
        self, 
        subtitle_content: List[Dict], 
        start_time: float, 
        end_time: float
    ) -> str:
        """获取指定时间范围内的字幕文本"""
        try:
            relevant_subtitles = []
            
            for entry in subtitle_content:
                entry_start = entry.get('start_time', 0.0)
                entry_end = entry.get('end_time', 0.0)
                
                # 检查字幕时间段是否与指定范围重叠
                if (entry_start <= end_time and entry_end >= start_time):
                    text = entry.get('text', '').strip()
                    if text:
                        relevant_subtitles.append({
                            'start_time': entry_start,
                            'end_time': entry_end,
                            'text': text
                        })
            
            # 按时间排序
            relevant_subtitles.sort(key=lambda x: x['start_time'])
            
            # 合并文本
            subtitle_text = ' '.join([sub['text'] for sub in relevant_subtitles])
            return subtitle_text.strip()
            
        except Exception as e:
            logger.error(f"Failed to extract subtitle text for timerange {start_time}-{end_time}: {e}")
            return ""
    
    def analyze_frame_with_subtitle_context(
        self,
        video_id: int,
        frame_path: str,
        frame_timestamp: float,
        context_window: float = 5.0,
        question: str = None
    ) -> Dict[str, Any]:
        """
        分析视频帧，结合字幕上下文信息
        
        Args:
            video_id: 视频ID
            frame_path: 帧图片路径
            frame_timestamp: 帧时间戳
            context_window: 字幕上下文窗口（秒）
            question: 自定义问题
            
        Returns:
            Dict containing analysis results with subtitle context
        """
        try:
            if not os.path.exists(frame_path):
                raise FileNotFoundError(f"Frame image not found: {frame_path}")
            
            # 获取字幕数据
            subtitle_data = self._get_video_subtitles(video_id)
            
            # 提取相关时间范围的字幕文本
            start_time = max(0, frame_timestamp - context_window)
            end_time = frame_timestamp + context_window
            
            subtitle_context = ""
            for subtitle in subtitle_data:
                subtitle_text = self._get_subtitle_text_for_timerange(
                    subtitle['content'], start_time, end_time
                )
                if subtitle_text:
                    subtitle_context += f"[{subtitle['language']}] {subtitle_text} "
            
            # 构建包含字幕上下文的问题
            if not question:
                if subtitle_context:
                    question = f"Analyze this video frame. The subtitle context around this moment is: '{subtitle_context.strip()}'. Describe what you see in the image and how it relates to the subtitle content."
                else:
                    question = "Describe what you see in this image in detail."
            else:
                if subtitle_context:
                    question = f"{question} Additional context from subtitles: '{subtitle_context.strip()}'"
            
            # 加载和处理图像
            image = Image.open(frame_path).convert('RGB')
            
            # 准备模型输入
            msgs = [{'role': 'user', 'content': [image, question]}]
            
            # 获取模型响应
            with torch.no_grad():
                answer = self.model.chat(
                    msgs=msgs,
                    tokenizer=self.tokenizer
                )
            
            result = {
                "frame_path": frame_path,
                "frame_timestamp": frame_timestamp,
                "question": question,
                "subtitle_context": subtitle_context.strip(),
                "context_window": context_window,
                "analysis": answer,
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            logger.info(f"Successfully analyzed frame with subtitle context: {frame_path}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze frame with subtitle context {frame_path}: {e}")
            raise
    
    def analyze_video_scenes_with_subtitles(
        self,
        video_id: int,
        scene_analysis_type: str = "content",
        context_window: float = 10.0
    ) -> Dict[str, Any]:
        """
        分析视频场景，结合字幕信息
        
        Args:
            video_id: 视频ID
            scene_analysis_type: 分析类型
            context_window: 字幕上下文窗口（秒）
            
        Returns:
            Dict containing scene analysis results with subtitle context
        """
        try:
            # 获取视频和关键帧
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                raise ValueError(f"Video {video_id} not found")
            
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).all()
            
            if not key_frames:
                raise ValueError(f"No key frames found for video {video_id}")
            
            # 获取字幕数据
            subtitle_data = self._get_video_subtitles(video_id)
            
            # 准备分析问题模板
            question_templates = {
                "content": "Analyze the main content, objects, and activities in this scene.",
                "emotion": "Analyze the emotional tone and mood of this scene.",
                "action": "Describe the actions and movements happening in this scene.",
                "characters": "Identify and describe any people or characters in this scene.",
                "setting": "Describe the setting, location, and environment of this scene.",
                "narrative": "Analyze the narrative elements and story progression in this scene."
            }
            
            base_question = question_templates.get(scene_analysis_type, question_templates["content"])
            
            # 分析每个关键帧
            scene_analyses = []
            for frame in key_frames:
                if os.path.exists(frame.file_path):
                    # 使用带字幕上下文的分析
                    analysis = self.analyze_frame_with_subtitle_context(
                        video_id=video_id,
                        frame_path=frame.file_path,
                        frame_timestamp=frame.timestamp,
                        context_window=context_window,
                        question=base_question
                    )
                    
                    analysis.update({
                        "frame_id": frame.id,
                        "frame_number": frame.frame_number,
                        "timestamp": frame.timestamp
                    })
                    scene_analyses.append(analysis)
            
            result = {
                "video_id": video_id,
                "analysis_type": scene_analysis_type,
                "total_scenes": len(scene_analyses),
                "scenes": scene_analyses,
                "subtitle_integration": True,
                "context_window": context_window,
                "available_subtitles": len(subtitle_data),
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            # 保存分析结果到数据库
            self._save_analysis_result(video_id, "minicpm_scene_analysis_with_subtitles", result)
            
            logger.info(f"Successfully analyzed {len(scene_analyses)} scenes with subtitles for video {video_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze video scenes with subtitles for video {video_id}: {e}")
            raise
    
    def analyze_video_content_summary_with_subtitles(self, video_id: int) -> Dict[str, Any]:
        """
        生成视频内容摘要，结合字幕信息
        
        Args:
            video_id: 视频ID
            
        Returns:
            Dict containing video content summary with subtitle integration
        """
        try:
            # 获取代表性关键帧
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).all()
            
            if not key_frames:
                raise ValueError(f"No key frames found for video {video_id}")
            
            # 获取字幕数据
            subtitle_data = self._get_video_subtitles(video_id)
            
            # 采样帧进行摘要分析
            sample_interval = max(1, len(key_frames) // 10)
            sampled_frames = key_frames[::sample_interval][:10]
            
            frame_paths = [f.file_path for f in sampled_frames if os.path.exists(f.file_path)]
            
            if not frame_paths:
                raise ValueError(f"No valid frame paths found for video {video_id}")
            
            # 获取整个视频的字幕文本摘要
            video = self.db.query(Video).filter(Video.id == video_id).first()
            video_duration = video.duration if video else 0
            
            full_subtitle_text = ""
            for subtitle in subtitle_data:
                subtitle_text = self._get_subtitle_text_for_timerange(
                    subtitle['content'], 0, video_duration
                )
                if subtitle_text:
                    full_subtitle_text += f"[{subtitle['language']}] {subtitle_text} "
            
            # 构建包含字幕信息的综合分析问题
            if full_subtitle_text.strip():
                question = f"""Analyze these key frames from a video and provide a comprehensive summary. 
                The video contains the following subtitle content: '{full_subtitle_text.strip()[:1000]}...' 
                
                Please provide analysis including:
                1) Main themes and content (considering both visual and subtitle information)
                2) Visual style and aesthetics
                3) Key scenes or moments and their narrative significance
                4) Overall story progression and character development
                5) How the visual content relates to the spoken/subtitle content
                6) Notable visual elements or patterns"""
            else:
                question = """Analyze these key frames from a video and provide a comprehensive summary including: 
                1) Main themes and content, 2) Visual style and aesthetics, 3) Key scenes or moments, 
                4) Overall narrative or progression, 5) Notable visual elements or patterns."""
            
            # 生成综合摘要
            summary_result = self.compare_frames(
                video_id,
                frame_paths,
                question
            )
            
            result = {
                "video_id": video_id,
                "summary": summary_result["comparison"],
                "analyzed_frames": len(frame_paths),
                "total_key_frames": len(key_frames),
                "frame_timestamps": [f.timestamp for f in sampled_frames],
                "subtitle_integration": True,
                "available_subtitles": len(subtitle_data),
                "subtitle_summary": full_subtitle_text.strip()[:500] + "..." if len(full_subtitle_text) > 500 else full_subtitle_text.strip(),
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            # 保存分析结果到数据库
            self._save_analysis_result(video_id, "minicpm_content_summary_with_subtitles", result)
            
            logger.info(f"Successfully generated content summary with subtitles for video {video_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate content summary with subtitles for video {video_id}: {e}")
            raise
    
    def analyze_dialogue_scene_alignment(self, video_id: int) -> Dict[str, Any]:
        """
        分析对话与场景的对齐情况
        
        Args:
            video_id: 视频ID
            
        Returns:
            Dict containing dialogue-scene alignment analysis
        """
        try:
            # 获取关键帧和字幕
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).all()
            
            subtitle_data = self._get_video_subtitles(video_id)
            
            if not key_frames or not subtitle_data:
                raise ValueError("Need both key frames and subtitles for dialogue-scene alignment analysis")
            
            alignment_analyses = []
            
            # 为每个关键帧分析对话与场景的对齐
            for frame in key_frames:
                if not os.path.exists(frame.file_path):
                    continue
                
                # 获取该帧时间点前后的字幕
                context_window = 3.0  # 3秒窗口
                start_time = max(0, frame.timestamp - context_window)
                end_time = frame.timestamp + context_window
                
                dialogue_context = ""
                for subtitle in subtitle_data:
                    subtitle_text = self._get_subtitle_text_for_timerange(
                        subtitle['content'], start_time, end_time
                    )
                    if subtitle_text:
                        dialogue_context += f"{subtitle_text} "
                
                if dialogue_context.strip():
                    # 分析对话与视觉内容的对齐
                    question = f"""Analyze this video frame in relation to the dialogue happening around this moment: 
                    '{dialogue_context.strip()}'
                    
                    Please analyze:
                    1) How well does the visual content match the dialogue?
                    2) Are there any visual cues that support or contradict the spoken content?
                    3) What emotions or reactions can you see that relate to the dialogue?
                    4) Is this a speaking scene, reaction scene, or narrative scene?
                    5) How does the visual composition enhance the dialogue?"""
                    
                    analysis = self.analyze_single_frame(
                        video_id, frame.file_path, question
                    )
                    
                    alignment_analyses.append({
                        "frame_id": frame.id,
                        "frame_number": frame.frame_number,
                        "timestamp": frame.timestamp,
                        "dialogue_context": dialogue_context.strip(),
                        "alignment_analysis": analysis["analysis"],
                        "context_window": context_window
                    })
            
            result = {
                "video_id": video_id,
                "total_alignments": len(alignment_analyses),
                "dialogue_scene_alignments": alignment_analyses,
                "model": "MiniCPM-V-4",
                "timestamp": self._get_current_timestamp()
            }
            
            # 保存分析结果
            self._save_analysis_result(video_id, "minicpm_dialogue_scene_alignment", result)
            
            logger.info(f"Successfully analyzed dialogue-scene alignment for video {video_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze dialogue-scene alignment for video {video_id}: {e}")
            raise