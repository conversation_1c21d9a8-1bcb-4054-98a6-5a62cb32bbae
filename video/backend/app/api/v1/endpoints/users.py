"""
用户管理API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import (
    get_current_admin_user, get_current_superuser,
    require_user_read, require_user_create, 
    require_user_update, require_user_delete
)
from app.services.user_service import UserService, RoleService
from app.schemas.user import (
    User, UserCreate, UserUpdate, UserAdminCreate, UserAdminUpdate,
    UserListResponse, Role
)
from app.models.user import User as UserModel


router = APIRouter()


@router.get("", response_model=UserListResponse, summary="获取用户列表")
async def get_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    current_user: UserModel = Depends(require_user_read),
    db: Session = Depends(get_db)
):
    """
    获取用户列表（需要用户读取权限）
    
    - **skip**: 跳过的记录数
    - **limit**: 返回的记录数
    - **search**: 搜索关键词（用户名、邮箱、全名）
    - **is_active**: 是否激活状态过滤
    """
    users, total = UserService.get_users(
        db, skip=skip, limit=limit, search=search, is_active=is_active
    )
    
    return {
        "users": users,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }


@router.get("/{user_id}", response_model=User, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: UserModel = Depends(require_user_read),
    db: Session = Depends(get_db)
):
    """
    获取指定用户的详细信息
    """
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.post("", response_model=User, summary="创建用户")
async def create_user(
    user_create: UserAdminCreate,
    current_user: UserModel = Depends(require_user_create),
    db: Session = Depends(get_db)
):
    """
    创建新用户（管理员功能）
    
    - **username**: 用户名
    - **email**: 邮箱地址
    - **password**: 密码
    - **full_name**: 全名
    - **is_active**: 是否激活
    - **is_superuser**: 是否为超级用户
    - **role_ids**: 角色ID列表
    """
    try:
        # 创建基础用户
        user = UserService.create_user(db, user_create)
        
        # 设置管理员属性
        if hasattr(user_create, 'is_active'):
            user.is_active = user_create.is_active
        if hasattr(user_create, 'is_superuser'):
            user.is_superuser = user_create.is_superuser
        
        # 分配角色
        if hasattr(user_create, 'role_ids') and user_create.role_ids:
            user.roles.clear()  # 清除默认角色
            for role_id in user_create.role_ids:
                role = RoleService.get_role_by_id(db, role_id)
                if role:
                    user.roles.append(role)
        
        db.commit()
        db.refresh(user)
        
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建用户失败"
        )


@router.put("/{user_id}", response_model=User, summary="更新用户")
async def update_user(
    user_id: int,
    user_update: UserAdminUpdate,
    current_user: UserModel = Depends(require_user_update),
    db: Session = Depends(get_db)
):
    """
    更新用户信息（管理员功能）
    """
    try:
        # 检查用户是否存在
        user = UserService.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 更新基础信息
        updated_user = UserService.update_user(db, user_id, user_update)
        
        # 更新管理员属性
        if hasattr(user_update, 'is_superuser') and user_update.is_superuser is not None:
            updated_user.is_superuser = user_update.is_superuser
        
        # 更新角色
        if hasattr(user_update, 'role_ids') and user_update.role_ids is not None:
            updated_user.roles.clear()
            for role_id in user_update.role_ids:
                role = RoleService.get_role_by_id(db, role_id)
                if role:
                    updated_user.roles.append(role)
        
        db.commit()
        db.refresh(updated_user)
        
        return updated_user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户失败"
        )


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    current_user: UserModel = Depends(require_user_delete),
    db: Session = Depends(get_db)
):
    """
    删除用户
    """
    # 不能删除自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己"
        )
    
    try:
        UserService.delete_user(db, user_id)
        return {"message": "用户删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )


@router.put("/{user_id}/activate", summary="激活用户")
async def activate_user(
    user_id: int,
    current_user: UserModel = Depends(require_user_update),
    db: Session = Depends(get_db)
):
    """
    激活用户
    """
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.is_active = True
    db.commit()
    
    return {"message": "用户已激活"}


@router.put("/{user_id}/deactivate", summary="停用用户")
async def deactivate_user(
    user_id: int,
    current_user: UserModel = Depends(require_user_update),
    db: Session = Depends(get_db)
):
    """
    停用用户
    """
    # 不能停用自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能停用自己"
        )
    
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    user.is_active = False
    db.commit()
    
    return {"message": "用户已停用"}


@router.get("/{user_id}/permissions", response_model=List[str], summary="获取用户权限")
async def get_user_permissions(
    user_id: int,
    current_user: UserModel = Depends(require_user_read),
    db: Session = Depends(get_db)
):
    """
    获取用户的所有权限列表
    """
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    permissions = UserService.get_user_permissions(db, user_id)
    return permissions


@router.get("/{user_id}/roles", response_model=List[Role], summary="获取用户角色")
async def get_user_roles(
    user_id: int,
    current_user: UserModel = Depends(require_user_read),
    db: Session = Depends(get_db)
):
    """
    获取用户的角色列表
    """
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user.roles


@router.put("/{user_id}/roles", summary="设置用户角色")
async def set_user_roles(
    user_id: int,
    role_ids: List[int],
    current_user: UserModel = Depends(require_user_update),
    db: Session = Depends(get_db)
):
    """
    设置用户的角色
    """
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 清除现有角色
    user.roles.clear()
    
    # 添加新角色
    for role_id in role_ids:
        role = RoleService.get_role_by_id(db, role_id)
        if role:
            user.roles.append(role)
    
    db.commit()
    
    return {"message": "用户角色设置成功"}
