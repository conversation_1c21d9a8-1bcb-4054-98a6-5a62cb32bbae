"""
任务执行日志工具
提供带时间戳的详细执行日志，方便观察全流程
"""

import time
from datetime import datetime
from loguru import logger
from typing import Optional, Dict, Any


class TaskLogger:
    """任务执行日志记录器"""
    
    def __init__(self, task_name: str, task_id: Optional[int] = None, video_id: Optional[int] = None):
        self.task_name = task_name
        self.task_id = task_id
        self.video_id = video_id
        self.start_time = time.time()
        self.step_start_time = None
        self.current_step = 0
        self.total_steps = 0
        
        # 创建任务标识符
        self.task_identifier = f"[{task_name}"
        if task_id:
            self.task_identifier += f" Task:{task_id}"
        if video_id:
            self.task_identifier += f" Video:{video_id}"
        self.task_identifier += "]"
    
    def start_task(self, total_steps: int = 0, description: str = ""):
        """开始任务执行"""
        self.total_steps = total_steps
        self.start_time = time.time()
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        logger.info(f"{timestamp} {self.task_identifier} 🚀 任务开始执行 - {description}")
        if total_steps > 0:
            logger.info(f"{timestamp} {self.task_identifier} 📋 总共 {total_steps} 个步骤")
    
    def start_step(self, step_name: str, step_number: Optional[int] = None):
        """开始执行步骤"""
        self.step_start_time = time.time()
        if step_number:
            self.current_step = step_number
        else:
            self.current_step += 1
            
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        elapsed = self.step_start_time - self.start_time
        
        progress_info = ""
        if self.total_steps > 0:
            progress_info = f" ({self.current_step}/{self.total_steps})"
        
        logger.info(f"{timestamp} {self.task_identifier} ⏳ 步骤{progress_info}: {step_name} [总耗时: {elapsed:.2f}s]")
    
    def complete_step(self, step_name: str, result_info: str = ""):
        """完成步骤执行"""
        if self.step_start_time is None:
            return
            
        step_duration = time.time() - self.step_start_time
        total_elapsed = time.time() - self.start_time
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        progress_info = ""
        if self.total_steps > 0:
            progress_info = f" ({self.current_step}/{self.total_steps})"
        
        logger.info(f"{timestamp} {self.task_identifier} ✅ 步骤{progress_info}完成: {step_name} [步骤耗时: {step_duration:.2f}s, 总耗时: {total_elapsed:.2f}s]")
        if result_info:
            logger.info(f"{timestamp} {self.task_identifier} 📊 结果: {result_info}")
    
    def log_info(self, message: str, data: Optional[Dict[str, Any]] = None):
        """记录信息日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        elapsed = time.time() - self.start_time
        logger.info(f"{timestamp} {self.task_identifier} ℹ️  {message} [总耗时: {elapsed:.2f}s]")
        if data:
            logger.info(f"{timestamp} {self.task_identifier} 📋 数据: {data}")
    
    def log_warning(self, message: str, error: Optional[Exception] = None):
        """记录警告日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        elapsed = time.time() - self.start_time
        logger.warning(f"{timestamp} {self.task_identifier} ⚠️  警告: {message} [总耗时: {elapsed:.2f}s]")
        if error:
            logger.warning(f"{timestamp} {self.task_identifier} 🔍 错误详情: {str(error)}")
    
    def log_error(self, message: str, error: Optional[Exception] = None):
        """记录错误日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        elapsed = time.time() - self.start_time
        logger.error(f"{timestamp} {self.task_identifier} ❌ 错误: {message} [总耗时: {elapsed:.2f}s]")
        if error:
            logger.error(f"{timestamp} {self.task_identifier} 🔍 错误详情: {str(error)}")
    
    def complete_task(self, success: bool = True, final_message: str = ""):
        """完成任务执行"""
        total_duration = time.time() - self.start_time
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        if success:
            logger.info(f"{timestamp} {self.task_identifier} 🎉 任务执行成功 - {final_message} [总耗时: {total_duration:.2f}s]")
        else:
            logger.error(f"{timestamp} {self.task_identifier} 💥 任务执行失败 - {final_message} [总耗时: {total_duration:.2f}s]")
    
    def log_progress(self, current: int, total: int, message: str = ""):
        """记录进度信息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        elapsed = time.time() - self.start_time
        percentage = (current / total * 100) if total > 0 else 0
        
        logger.info(f"{timestamp} {self.task_identifier} 📈 进度: {current}/{total} ({percentage:.1f}%) - {message} [总耗时: {elapsed:.2f}s]")
    
    def log_performance(self, operation: str, duration: float, additional_info: str = ""):
        """记录性能信息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        total_elapsed = time.time() - self.start_time
        
        logger.info(f"{timestamp} {self.task_identifier} ⚡ 性能: {operation} 耗时 {duration:.2f}s {additional_info} [总耗时: {total_elapsed:.2f}s]")