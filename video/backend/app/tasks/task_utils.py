"""
任务处理相关的通用工具函数
"""

from celery import current_task
from loguru import logger
from sqlalchemy.orm import Session
from app.models.task import Video, AnalysisResult, Task

def safe_update_task_state(task_instance, state, meta=None):
    """安全地更新Celery任务状态，处理非Celery环境"""
    try:
        if hasattr(task_instance, 'request') and task_instance.request.id:
            if state == 'PROGRESS':
                current_task.update_state(state=state, meta=meta)
            else:
                task_instance.update_state(state=state, meta=meta)
    except Exception:
        # 记录日志
        logger.error(f"Failed to update task state: {state}, meta: {meta}")
        pass  # 忽略非Celery环境的错误

def check_task_status(task_id: int, db: Session) -> bool:
    """检查任务状态，如果被暂停则返回False"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        return False
    return task.status not in ["paused", "cancelled"]

