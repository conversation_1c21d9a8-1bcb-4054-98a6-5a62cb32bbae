#!/usr/bin/env python3
"""
测试字幕生成功能的脚本
"""

import os
import sys
import traceback

def test_environment():
    """测试环境变量"""
    print("=== 环境变量检查 ===")
    print(f"ENABLE_PARSE_AUDIO: {os.getenv('ENABLE_PARSE_AUDIO', 'not set')}")
    print(f"AUDIO_GPU_IDS: {os.getenv('AUDIO_GPU_IDS', 'not set')}")
    print(f"CUDA_VISIBLE_DEVICES: {os.getenv('CUDA_VISIBLE_DEVICES', 'not set')}")
    print()

def test_dependencies():
    """测试依赖包"""
    print("=== 依赖包检查 ===")
    
    # 测试 soundfile
    try:
        import soundfile
        print(f"✅ soundfile 已安装: {soundfile.__version__}")
    except ImportError as e:
        print(f"❌ soundfile 未安装: {e}")
        return False
    
    # 测试 modelscope
    try:
        import modelscope
        print(f"✅ modelscope 已安装")
    except ImportError as e:
        print(f"❌ modelscope 未安装: {e}")
        return False
    
    # 测试 modelscope pipeline
    try:
        from modelscope.pipelines import pipeline
        from modelscope.utils.constant import Tasks
        print(f"✅ modelscope pipeline 可用")
    except ImportError as e:
        print(f"❌ modelscope pipeline 不可用: {e}")
        return False
    
    print()
    return True

def test_audio_service():
    """测试音频服务"""
    print("=== 音频服务检查 ===")
    
    try:
        from app.services.local_audio_service import LocalAudioService
        service = LocalAudioService()
        
        print(f"音频服务可用性: {service.is_available()}")
        
        if service.is_available():
            print("✅ 音频服务初始化成功")
            return True
        else:
            print("❌ 音频服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 音频服务初始化失败: {e}")
        traceback.print_exc()
        return False

def test_subtitle_service():
    """测试字幕服务"""
    print("=== 字幕服务检查 ===")
    
    try:
        from app.services.subtitle_service import SubtitleService
        from app.core.database import SessionLocal
        
        db = SessionLocal()
        service = SubtitleService(db)
        print("✅ 字幕服务初始化成功")
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 字幕服务初始化失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("字幕生成功能测试")
    print("=" * 50)
    
    # 测试环境变量
    test_environment()
    
    # 测试依赖包
    deps_ok = test_dependencies()
    if not deps_ok:
        print("❌ 依赖包检查失败，请安装缺失的依赖")
        return False
    
    # 测试音频服务
    audio_ok = test_audio_service()
    if not audio_ok:
        print("❌ 音频服务检查失败")
        return False
    
    # 测试字幕服务
    subtitle_ok = test_subtitle_service()
    if not subtitle_ok:
        print("❌ 字幕服务检查失败")
        return False
    
    print("✅ 所有检查通过，字幕生成功能应该可以正常工作")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
