["test_configuration_validator.py::TestConfigurationValidator::test_allowed_values", "test_configuration_validator.py::TestConfigurationValidator::test_cross_field_validation_equals", "test_configuration_validator.py::TestConfigurationValidator::test_cross_field_validation_exists", "test_configuration_validator.py::TestConfigurationValidator::test_database_connection_failure", "test_configuration_validator.py::TestConfigurationValidator::test_database_connection_success", "test_configuration_validator.py::TestConfigurationValidator::test_deprecated_values", "test_configuration_validator.py::TestConfigurationValidator::test_external_service_connection_routing", "test_configuration_validator.py::TestConfigurationValidator::test_forbidden_values", "test_configuration_validator.py::TestConfigurationValidator::test_http_connection_failure", "test_configuration_validator.py::TestConfigurationValidator::test_http_connection_success", "test_configuration_validator.py::TestConfigurationValidator::test_redis_connection_failure", "test_configuration_validator.py::TestConfigurationValidator::test_redis_connection_success", "test_configuration_validator.py::TestConfigurationValidator::test_unknown_value_type", "test_configuration_validator.py::TestConfigurationValidator::test_validate_boolean_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_boolean_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_directory_path_must_exist", "test_configuration_validator.py::TestConfigurationValidator::test_validate_directory_path_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_email_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_email_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_file_path_extension", "test_configuration_validator.py::TestConfigurationValidator::test_validate_file_path_must_exist", "test_configuration_validator.py::TestConfigurationValidator::test_validate_file_path_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_float_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_float_range", "test_configuration_validator.py::TestConfigurationValidator::test_validate_float_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_integer_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_integer_range", "test_configuration_validator.py::TestConfigurationValidator::test_validate_integer_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_json_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_json_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_list_length", "test_configuration_validator.py::TestConfigurationValidator::test_validate_list_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_regex_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_regex_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_string_empty_not_required", "test_configuration_validator.py::TestConfigurationValidator::test_validate_string_empty_required", "test_configuration_validator.py::TestConfigurationValidator::test_validate_string_length_constraints", "test_configuration_validator.py::TestConfigurationValidator::test_validate_string_pattern", "test_configuration_validator.py::TestConfigurationValidator::test_validate_string_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_url_invalid", "test_configuration_validator.py::TestConfigurationValidator::test_validate_url_scheme_restriction", "test_configuration_validator.py::TestConfigurationValidator::test_validate_url_valid", "test_configuration_validator.py::TestConfigurationValidator::test_validation_exception_handling"]