{"test_configuration_validator.py::TestConfigurationValidator::test_validate_string_empty_required": true, "test_configuration_validator.py::TestConfigurationValidator::test_validate_float_invalid": true, "test_configuration_validator.py::TestConfigurationValidator::test_validate_url_invalid": true, "test_configuration_validator.py::TestConfigurationValidator::test_validation_exception_handling": true}