#!/usr/bin/env python3
"""
Test script for audio processing integration
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.local_audio_service import local_audio_service, AUDIO_DEPENDENCIES_AVAILABLE


def test_dependencies():
    """Test if audio processing dependencies are available"""
    print("Testing audio processing dependencies...")
    print(f"Dependencies available: {AUDIO_DEPENDENCIES_AVAILABLE}")
    print(f"Service available: {local_audio_service.is_available()}")
    
    if not AUDIO_DEPENDENCIES_AVAILABLE:
        print("\nMissing dependencies. Please install:")
        print("pip install GPUtil soundfile librosa modelscope huggingface_hub")
        return False
    
    return True


def test_audio_service():
    """Test basic audio service functionality"""
    print("\nTesting audio service...")
    
    try:
        # Test service availability
        available = local_audio_service.is_available()
        print(f"Audio service available: {available}")
        
        if not available:
            print("Audio service is not available")
            return False
            
        # Test model info
        model_info = local_audio_service.model_manager.get_model_info()
        print(f"Model info: {model_info}")
        
        return True
        
    except Exception as e:
        print(f"Error testing audio service: {e}")
        return False


async def test_audio_processing():
    """Test audio processing with a sample file"""
    print("\nTesting audio processing...")
    
    # Create a simple test audio file (if possible)
    try:
        import numpy as np
        import soundfile as sf
        
        # Generate a simple sine wave
        sample_rate = 16000
        duration = 2  # seconds
        frequency = 440  # Hz
        
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio_data = np.sin(2 * np.pi * frequency * t)
        
        # Save test audio file
        test_file = "/tmp/test_audio.wav"
        sf.write(test_file, audio_data, sample_rate)
        
        print(f"Created test audio file: {test_file}")
        
        # Test processing
        if local_audio_service.is_available():
            try:
                result = await local_audio_service.process_audio(test_file)
                print(f"Processing result: {result}")
                
                # Test subtitle generation
                subtitles = local_audio_service.generate_subtitle_from_audio(test_file)
                print(f"Generated subtitles: {len(subtitles)} entries")
                
                # Clean up
                os.remove(test_file)
                return True
                
            except Exception as e:
                print(f"Error processing audio: {e}")
                # Clean up
                if os.path.exists(test_file):
                    os.remove(test_file)
                return False
        else:
            print("Audio service not available for processing test")
            return False
            
    except ImportError as e:
        print(f"Cannot create test audio file: {e}")
        return False
    except Exception as e:
        print(f"Error in audio processing test: {e}")
        return False


def main():
    """Main test function"""
    print("Audio Processing Integration Test")
    print("=" * 40)
    
    # Test dependencies
    if not test_dependencies():
        print("\n❌ Dependencies test failed")
        return 1
    
    print("✅ Dependencies test passed")
    
    # Test service
    if not test_audio_service():
        print("\n❌ Audio service test failed")
        return 1
    
    print("✅ Audio service test passed")
    
    # Test processing
    try:
        result = asyncio.run(test_audio_processing())
        if result:
            print("✅ Audio processing test passed")
        else:
            print("❌ Audio processing test failed")
            return 1
    except Exception as e:
        print(f"❌ Audio processing test failed with exception: {e}")
        return 1
    
    print("\n🎉 All tests passed!")
    print("\nAudio processing integration is working correctly.")
    print("You can now use the subtitle generation feature.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
