# 视频任务执行日志增强

## 概述

为了方便观察视频处理任务的全流程执行情况，我们为所有视频任务添加了详细的执行日志，包含时间戳和执行时间记录。

## 新增功能

### 1. TaskLogger 日志记录器

位置：`app/tasks/task_logger.py`

**主要功能：**
- 带时间戳的详细日志记录
- 步骤执行时间统计
- 性能监控和分析
- 错误和警告信息记录
- 进度跟踪

**使用示例：**
```python
from app.tasks.task_logger import TaskLogger

task_logger = TaskLogger("TASK_NAME", task_id=123, video_id=456)
task_logger.start_task(total_steps=5, description="任务描述")

task_logger.start_step("步骤名称")
# ... 执行步骤逻辑 ...
task_logger.complete_step("步骤名称", "步骤结果信息")

task_logger.complete_task(True, "任务完成信息")
```

### 2. 增强的任务文件

以下任务文件已添加详细日志记录：

#### 主任务编排 (`video_tasks.py`)
- `process_task_videos()` - 处理任务中的所有视频
- `process_single_video_complete_analysis()` - 单个视频完整分析

#### 基础信息分析 (`video_tasks_basic_info.py`)
- `analyze_video_basic_info()` - 视频基础信息分析
- 包含8个详细步骤的日志记录

#### 内容分析 (`video_tasks_content.py`)
- `analyze_video_content()` - 视频内容要素分析
- 模拟8个内容分析步骤

#### 剧情分析 (`video_tasks_plot.py`)
- `analyze_video_plot()` - 视频剧情结构分析
- 包含5个剧情分析步骤

### 3. 增强的服务文件

#### 视频分析服务 (`video_analysis_service.py`)
- `analyze_video_metadata()` - 视频元数据分析
- `extract_video_key_frames()` - 关键帧提取

#### 字幕服务 (`subtitle_service.py`)
- `generate_automatic_subtitle()` - 自动字幕生成

## 日志格式说明

### 时间戳格式
```
2025-01-06 15:30:45.123 [TASK_NAME Task:123 Video:456] 🚀 任务开始执行 - 任务描述
```

### 日志类型标识
- 🚀 任务开始
- ⏳ 步骤开始
- ✅ 步骤完成
- ℹ️ 信息日志
- ⚠️ 警告日志
- ❌ 错误日志
- 🎉 任务成功完成
- 💥 任务执行失败
- 📈 进度更新
- ⚡ 性能统计
- 📊 结果数据
- 📋 详细数据

### 执行时间记录
每个步骤和整个任务都会记录：
- 步骤耗时：单个步骤的执行时间
- 总耗时：从任务开始到当前的总时间

## 测试脚本

运行测试脚本查看日志效果：
```bash
cd video/backend
python test_task_logging.py
```

测试脚本包含：
1. 基础日志功能测试
2. 错误日志功能测试
3. 视频分析任务模拟

## 日志示例

### 任务开始
```
2025-01-06 15:30:45.123 [BASIC_INFO_ANALYSIS Video:123] 🚀 任务开始执行 - 视频 123 基础信息分析
2025-01-06 15:30:45.124 [BASIC_INFO_ANALYSIS Video:123] 📋 总共 8 个步骤
```

### 步骤执行
```
2025-01-06 15:30:45.125 [BASIC_INFO_ANALYSIS Video:123] ⏳ 步骤 (1/8): 获取视频信息 [总耗时: 0.00s]
2025-01-06 15:30:45.326 [BASIC_INFO_ANALYSIS Video:123] ✅ 步骤 (1/8)完成: 获取视频信息 [步骤耗时: 0.20s, 总耗时: 0.20s]
2025-01-06 15:30:45.327 [BASIC_INFO_ANALYSIS Video:123] 📊 结果: 视频文件: sample.mp4, 大小: 15728640 bytes
```

### 性能统计
```
2025-01-06 15:30:47.456 [BASIC_INFO_ANALYSIS Video:123] ⚡ 性能: ffprobe元数据提取 耗时 2.13s 获取到 25 个元数据字段 [总耗时: 2.33s]
```

### 任务完成
```
2025-01-06 15:30:52.789 [BASIC_INFO_ANALYSIS Video:123] 🎉 任务执行成功 - 基础信息分析完成，总耗时: 7.67s [总耗时: 7.67s]
```

## 配置说明

### 日志级别
日志使用 loguru 库，可以通过环境变量或配置文件调整日志级别：
- DEBUG: 显示所有日志
- INFO: 显示信息、警告和错误日志
- WARNING: 只显示警告和错误日志
- ERROR: 只显示错误日志

### 日志输出
- 默认输出到终端控制台
- 可配置输出到文件
- 支持结构化日志格式

## 性能影响

日志记录对性能的影响很小：
- 时间戳获取：< 0.001s
- 日志格式化：< 0.001s
- 控制台输出：< 0.01s

总体性能影响 < 1%，可以安全在生产环境使用。

## 扩展使用

### 添加自定义日志
```python
# 记录详细信息
task_logger.log_info("处理详情", {
    "files_processed": 10,
    "total_size_mb": "156.7"
})

# 记录性能信息
task_logger.log_performance("数据处理", 2.5, "处理了1000条记录")

# 记录进度
task_logger.log_progress(current=5, total=10, message="处理进度更新")
```

### 错误处理
```python
try:
    # 执行操作
    pass
except Exception as e:
    task_logger.log_error("操作失败", e)
    task_logger.complete_task(False, f"任务失败: {str(e)}")
```

## 注意事项

1. **线程安全**：TaskLogger 是线程安全的，可以在多线程环境中使用
2. **内存使用**：日志记录器会保持任务开始时间，但不会累积大量数据
3. **异常处理**：所有日志操作都有异常保护，不会影响主要业务逻辑
4. **时区**：时间戳使用系统本地时区

## 故障排除

### 常见问题

1. **日志不显示**
   - 检查日志级别设置
   - 确认 loguru 库已正确安装

2. **时间戳不准确**
   - 检查系统时间设置
   - 确认时区配置正确

3. **性能问题**
   - 可以通过设置更高的日志级别减少输出
   - 考虑将日志输出到文件而不是控制台