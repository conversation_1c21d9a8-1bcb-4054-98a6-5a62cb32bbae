# MiniCPM-V-4 Integration Guide

## Overview

This integration adds MiniCPM-V-4 vision language model capabilities to the video analysis system, enabling advanced visual understanding and analysis of video content.

## Features

- **Single Frame Analysis**: Analyze individual video frames with custom questions
- **Frame Comparison**: Compare multiple frames to detect changes and differences
- **Scene Analysis**: Analyze video scenes for content, emotion, actions, characters, and settings
- **Scene Change Detection**: Automatically detect scene transitions using visual analysis
- **Content Summary**: Generate comprehensive video content summaries from key frames
- **Subtitle Integration**: Combine visual analysis with subtitle/dialogue context for enhanced understanding
- **Dialogue-Scene Alignment**: Analyze how visual content aligns with spoken dialogue
- **Contextual Analysis**: Use subtitle context to provide more accurate and relevant visual analysis

## Installation

### 1. Install Dependencies

```bash
# Install MiniCPM-V-4 specific dependencies
pip install -r requirements-minicpm.txt

# For CUDA support (choose appropriate version)
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 2. Model Download

The model will be automatically downloaded on first use. Ensure you have sufficient disk space (~8GB) and internet connectivity.

### 3. Environment Configuration

Add the following to your `.env` file:

```env
# MiniCPM-V-4 Configuration
MINICPM_MODEL_NAME=openbmb/MiniCPM-V-4
MINICPM_DEVICE=cuda
MINICPM_TORCH_DTYPE=bfloat16
MINICPM_ATTENTION_IMPLEMENTATION=sdpa
MINICPM_MAX_FRAMES_PER_ANALYSIS=10
MINICPM_FRAME_SAMPLING_INTERVAL=5
```

## API Endpoints

### Video Analysis Endpoints

#### 1. Analyze Single Frame
```http
POST /api/v1/videos/{video_id}/minicpm/analyze-frame
Content-Type: application/x-www-form-urlencoded

frame_path=path/to/frame.jpg&question=Describe what you see in this image
```

#### 2. Compare Frames
```http
POST /api/v1/videos/{video_id}/minicpm/compare-frames
Content-Type: application/x-www-form-urlencoded

frame_paths=path1.jpg,path2.jpg&question=Compare these images
```

#### 3. Analyze Video Scenes
```http
POST /api/v1/videos/{video_id}/minicpm/analyze-scenes
Content-Type: application/x-www-form-urlencoded

analysis_type=content
```

Analysis types:
- `content`: Main content, objects, and activities
- `emotion`: Emotional tone and mood
- `action`: Actions and movements
- `characters`: People and characters
- `setting`: Location and environment

#### 4. Detect Scene Changes
```http
POST /api/v1/videos/{video_id}/minicpm/detect-scene-changes
Content-Type: application/x-www-form-urlencoded

comparison_threshold=5
```

#### 5. Generate Content Summary
```http
POST /api/v1/videos/{video_id}/minicpm/content-summary
```

#### 6. Get Model Information
```http
GET /api/v1/videos/minicpm/model-info
```

### Subtitle Integration Endpoints

#### 1. Analyze Frame with Subtitle Context
```http
POST /api/v1/videos/{video_id}/minicpm/analyze-frame-with-subtitles
Content-Type: application/x-www-form-urlencoded

frame_path=path/to/frame.jpg&frame_timestamp=120.5&context_window=5.0&question=Analyze this scene
```

#### 2. Analyze Scenes with Subtitles
```http
POST /api/v1/videos/{video_id}/minicpm/analyze-scenes-with-subtitles
Content-Type: application/x-www-form-urlencoded

analysis_type=content&context_window=10.0
```

#### 3. Generate Content Summary with Subtitles
```http
POST /api/v1/videos/{video_id}/minicpm/content-summary-with-subtitles
```

#### 4. Analyze Dialogue-Scene Alignment
```http
POST /api/v1/videos/{video_id}/minicpm/dialogue-scene-alignment
```

#### 5. Comprehensive Analysis with Subtitles
```http
POST /api/v1/videos/{video_id}/minicpm/comprehensive-analysis-with-subtitles
```

### Task-Level Endpoints

#### 1. Start Task with MiniCPM-V-4
```http
POST /api/v1/tasks/{task_id}/start-with-minicpm
```

#### 2. Single Video MiniCPM-V-4 Analysis
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-analysis
```

#### 3. Scene Analysis for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-scene-analysis
Content-Type: application/x-www-form-urlencoded

analysis_type=content
```

#### 4. Scene Change Detection for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-scene-changes
Content-Type: application/x-www-form-urlencoded

comparison_threshold=5
```

#### 5. Content Summary for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-content-summary
```

#### 6. Get MiniCPM-V-4 Results
```http
GET /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-results
```

### Task-Level Subtitle Integration Endpoints

#### 1. Scene Analysis with Subtitles for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-scenes-with-subtitles
Content-Type: application/x-www-form-urlencoded

analysis_type=content&context_window=10.0
```

#### 2. Content Summary with Subtitles for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-summary-with-subtitles
```

#### 3. Dialogue-Scene Alignment for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-dialogue-alignment
```

#### 4. Comprehensive Analysis with Subtitles for Task Video
```http
POST /api/v1/tasks/{task_id}/videos/{video_id}/minicpm-comprehensive-with-subtitles
```

## Celery Tasks

The integration includes several Celery tasks for asynchronous processing:

**Basic MiniCPM-V-4 Tasks:**
- `analyze_video_with_minicpm_scenes`: Scene analysis task
- `detect_scene_changes_with_minicpm`: Scene change detection task
- `generate_video_content_summary_with_minicpm`: Content summary generation task
- `comprehensive_minicpm_analysis`: Complete MiniCPM-V-4 analysis
- `analyze_specific_frames_with_minicpm`: Analyze specific frames

**Subtitle Integration Tasks:**
- `analyze_video_scenes_with_subtitles`: Scene analysis with subtitle context
- `generate_video_content_summary_with_subtitles`: Content summary with subtitle integration
- `analyze_dialogue_scene_alignment`: Dialogue-scene alignment analysis
- `comprehensive_minicpm_analysis_with_subtitles`: Complete analysis with subtitle integration

**Workflow Tasks:**
- `process_single_video_with_minicpm_analysis`: Complete video analysis with MiniCPM-V-4
- `process_task_videos_with_minicpm`: Process all videos in a task with MiniCPM-V-4

## Usage Examples

### Python Client Example

```python
import requests

# Analyze video scenes (basic)
response = requests.post(
    "http://localhost:8000/api/v1/videos/1/minicpm/analyze-scenes",
    data={"analysis_type": "content"}
)
result = response.json()
print(f"Analyzed {result['data']['total_scenes']} scenes")

# Analyze video scenes with subtitles
response = requests.post(
    "http://localhost:8000/api/v1/videos/1/minicpm/analyze-scenes-with-subtitles",
    data={"analysis_type": "content", "context_window": 10.0}
)
result = response.json()
print(f"Analyzed {result['data']['total_scenes']} scenes with subtitle integration")

# Generate content summary with subtitles
response = requests.post(
    "http://localhost:8000/api/v1/videos/1/minicpm/content-summary-with-subtitles"
)
summary = response.json()
print(f"Summary with subtitles: {summary['data']['summary']}")

# Analyze dialogue-scene alignment
response = requests.post(
    "http://localhost:8000/api/v1/videos/1/minicpm/dialogue-scene-alignment"
)
alignment = response.json()
print(f"Dialogue alignments: {alignment['data']['total_alignments']}")
```

### JavaScript/Frontend Example

```javascript
// Start MiniCPM-V-4 analysis for a task
const startAnalysis = async (taskId) => {
  const response = await fetch(`/api/v1/tasks/${taskId}/start-with-minicpm`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  });
  
  const result = await response.json();
  console.log('Analysis started:', result.message);
};

// Get analysis results
const getResults = async (taskId, videoId) => {
  const response = await fetch(`/api/v1/tasks/${taskId}/videos/${videoId}/minicpm-results`);
  const results = await response.json();
  
  console.log('Analysis results:', results.analysis_results);
};
```

## Performance Considerations

1. **GPU Memory**: MiniCPM-V-4 requires significant GPU memory (~8GB VRAM recommended)
2. **Processing Time**: Vision analysis takes longer than traditional methods
3. **Concurrent Tasks**: Limit concurrent MiniCPM-V-4 tasks based on available GPU memory
4. **Frame Sampling**: Adjust `MINICPM_MAX_FRAMES_PER_ANALYSIS` based on your hardware

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce `MINICPM_MAX_FRAMES_PER_ANALYSIS`
   - Use CPU mode by setting `MINICPM_DEVICE=cpu`
   - Enable model quantization

2. **Model Download Issues**
   - Ensure internet connectivity
   - Check Hugging Face access
   - Verify disk space availability

3. **Import Errors**
   - Install all dependencies from `requirements-minicpm.txt`
   - Check PyTorch CUDA compatibility

### Logging

MiniCPM-V-4 operations are logged with detailed information:

```python
# Check logs for MiniCPM-V-4 operations
import logging
logging.getLogger('app.services.minicpm_v4_service').setLevel(logging.DEBUG)
```

## Integration with Existing Workflow

The MiniCPM-V-4 integration seamlessly works with the existing video analysis pipeline:

1. **Traditional Analysis**: Basic info → Content → Plot analysis
2. **Enhanced Analysis**: Traditional analysis + MiniCPM-V-4 comprehensive analysis
3. **Standalone Analysis**: Use MiniCPM-V-4 endpoints independently

Results are stored in the same `AnalysisResult` table with specific step names:

**Basic Analysis Steps:**
- `minicpm_scene_analysis`
- `minicpm_scene_changes`
- `minicpm_content_summary`

**Subtitle Integration Steps:**
- `minicpm_scene_analysis_with_subtitles`
- `minicpm_content_summary_with_subtitles`
- `minicpm_dialogue_scene_alignment`

## Subtitle Integration Benefits

1. **Enhanced Context Understanding**: Visual analysis is enriched with dialogue and narrative context
2. **Improved Accuracy**: Subtitle information helps disambiguate visual content
3. **Narrative Analysis**: Better understanding of story progression and character development
4. **Dialogue-Visual Alignment**: Analyze how spoken content relates to visual scenes
5. **Multi-modal Analysis**: Combine text and visual information for comprehensive understanding

## Future Enhancements

- Multi-language support for questions and responses
- Custom model fine-tuning capabilities
- Batch processing optimizations
- Integration with other vision models
- Real-time video stream analysis
- Advanced subtitle timing synchronization
- Emotion detection from both visual and textual content
- Character identification and tracking across scenes