import { createRouter, createWebHistory } from 'vue-router'

// 页面组件懒加载
const TaskManagement = () => import('@/views/TaskManagement/TaskList.vue')
const VideoAnalysis = () => import('@/views/VideoAnalysis/AnalysisWorkspace.vue')
const VideoAnalysisDetail = () => import('@/views/VideoAnalysis/AnalysisDetail.vue')
const ClipManagement = () => import('@/views/ClipManagement/ClipGrid.vue')
const EditingWorkspace = () => import('@/views/EditingWorkspace/Timeline.vue')
const TaskDetail = () => import('@/views/TaskManagement/TaskDetail.vue')

// 认证相关组件
const Login = () => import('@/views/Auth/Login.vue')
const Register = () => import('@/views/Auth/Register.vue')
const ForgotPassword = () => import('@/views/Auth/ForgotPassword.vue')
const UserProfile = () => import('@/views/User/Profile.vue')

// 管理员组件
const UserManagement = () => import('@/views/Admin/UserManagement.vue')

const routes = [
  {
    path: '/',
    redirect: '/tasks'
  },
  // 认证路由
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideForAuth: true // 已登录用户隐藏
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: '用户注册',
      requiresAuth: false,
      hideForAuth: true // 已登录用户隐藏
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    meta: {
      title: '忘记密码',
      requiresAuth: false,
      hideForAuth: true // 已登录用户隐藏
    }
  },
  {
    path: '/profile',
    name: 'UserProfile',
    component: UserProfile,
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },
  // 管理员路由
  {
    path: '/admin/users',
    name: 'UserManagement',
    component: UserManagement,
    meta: {
      title: '用户管理',
      requiresAuth: true,
      requiresAdmin: true
    }
  },
  {
    path: '/tasks',
    name: 'TaskManagement',
    component: TaskManagement,
    meta: {
      title: '任务管理',
      requiresAuth: true
    }
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetail',
    component: TaskDetail,
    meta: {
      title: '任务详情',
      requiresAuth: true
    }
  },
  // {
  //   path: '/analysis',
  //   name: 'VideoAnalysis',
  //   component: VideoAnalysis,
  //   meta: {
  //     title: '视频分析',
  //     requiresAuth: true
  //   }
  // },
  {
    path: '/analysis/:videoId',
    name: 'VideoAnalysisDetail',
    component: VideoAnalysisDetail,
    meta: {
      title: '分析详情',
      requiresAuth: true
    }
  },
  {
    path: '/clips',
    name: 'ClipManagement',
    component: ClipManagement,
    meta: {
      title: '片段管理',
      requiresAuth: true
    }
  },
  {
    path: '/clips/:id',
    name: 'ClipDetail',
    component: () => import('@/views/ClipManagement/ClipDetail.vue'),
    meta: {
      title: '片段详情',
      requiresAuth: true
    }
  },
  // {
  //   path: '/editor',
  //   name: 'EditingWorkspace',
  //   component: EditingWorkspace,
  //   meta: {
  //     title: '剪辑工作台',
  //     requiresAuth: true
  //   }
  // },
  // {
  //   path: '/editor/:projectId',
  //   name: 'ProjectEditor',
  //   component: () => import('@/views/EditingWorkspace/ProjectEditor.vue'),
  //   meta: {
  //     title: '项目编辑',
  //     requiresAuth: true
  //   }
  // },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 动态导入 auth store 以避免循环依赖
  const { useAuthStore } = await import('@/stores/auth')
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 元视 - 短剧版`
  }

  // 如果有 token 但没有用户信息，等待认证初始化完成
  if (authStore.token && !authStore.user && !authStore.isLoading) {
    try {
      await authStore.initAuth()
      console.log('认证状态初始化完成:2', authStore.isAuthenticated)
    } catch (error) {
      console.error('路由守卫中认证初始化失败:', error)
    }
  }

  // 认证检查
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    if (!authStore.isAuthenticated) {
      // 未登录，重定向到登录页面
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      // 无管理员权限，重定向到首页
      next('/')
      return
    }
  }

  // 已登录用户访问登录/注册页面时重定向到首页
  if (to.meta.hideForAuth && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

export default router
