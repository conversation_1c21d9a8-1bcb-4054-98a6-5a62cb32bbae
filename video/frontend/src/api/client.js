import axios from 'axios'

// 创建axios实例
const client = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 全局状态管理器引用（延迟初始化）
let appStore = null

// 初始化 store 引用的函数
export const initializeStoreRef = (store) => {
  appStore = store
}

// 请求拦截器
client.interceptors.request.use(
  (config) => {
    // 显示加载状态
    if (appStore) {
      appStore.setLoading(true)
    }

    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    if (appStore) {
      appStore.setLoading(false)
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
client.interceptors.response.use(
  (response) => {
    // 隐藏加载状态
    if (appStore) {
      appStore.setLoading(false)
    }

    return response.data
  },
  (error) => {
    if (appStore) {
      appStore.setLoading(false)
    }

    // 处理错误
    let errorMessage = '请求失败'

    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response

      switch (status) {
        case 400:
          errorMessage = data.detail || '请求参数错误'
          break
        case 401:
          errorMessage = '未授权，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('access_token')
          window.location.href = '/login'
          break
        case 403:
          errorMessage = '权限不足'
          break
        case 404:
          errorMessage = '资源不存在'
          break
        case 422:
          errorMessage = data.detail || '数据验证失败'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = data.detail || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }

    // 显示错误通知
    if (appStore) {
      appStore.showError('请求失败', errorMessage)
    } else {
      console.error('请求失败:', errorMessage)
    }

    return Promise.reject(error)
  }
)

export default client
