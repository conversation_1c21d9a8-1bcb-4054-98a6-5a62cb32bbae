<template>
  <div class="bg-gray-50 rounded-lg p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-3">视频帧时间轴</h3>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
      <span class="ml-2 text-sm text-gray-500">加载帧数据中...</span>
    </div>

    <!-- 帧时间轴 -->
    <div v-else-if="frames.length > 0" class="space-y-4">
      <!-- 时间轴容器 -->
      <div class="relative">
        <!-- 滚动控制按钮 -->
        <div class="flex items-center justify-between mb-2">
          <button
            @click="scrollLeft"
            :disabled="scrollPosition <= 0"
            class="p-2 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 disabled:text-gray-400 rounded-md transition-colors"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <span class="text-sm text-gray-500">
            {{ Math.round(scrollPosition) }}px / {{ Math.round(maxScrollPosition) }}px
          </span>
          <button
            @click="scrollRight"
            :disabled="scrollPosition >= maxScrollPosition"
            class="p-2 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 disabled:text-gray-400 rounded-md transition-colors"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <!-- 时间轴背景 -->
        <div class="relative h-20 bg-white rounded-lg border overflow-hidden">
          <!-- 可滚动的帧容器 -->
          <div
            ref="frameContainer"
            class="h-full overflow-x-auto overflow-y-hidden scrollbar-hide"
            @scroll="onScroll"
          >
            <!-- 帧缩略图 -->
            <div class="flex h-full" :style="{ width: totalWidth + 'px' }">
              <div
                v-for="(frame, index) in frames"
                :key="frame.id"
                class="relative flex-shrink-0 cursor-pointer transition-all duration-200 hover:z-10"
                :style="{ width: frameWidth + 'px' }"
                @click="selectFrame(frame)"
                @mouseenter="showTooltip(frame, $event)"
                @mouseleave="hideTooltip"
              >
                <!-- 帧图片 -->
                <img
                  :src="getFrameUrl(frame.file_path)"
                  :alt="`Frame at ${formatTime(frame.timestamp)}`"
                  class="w-full h-full object-cover border-r border-gray-200 cursor-pointer"
                  :class="{
                    'ring-2 ring-primary-500': selectedFrame?.id === frame.id,
                    'opacity-75': !frame.is_key_frame
                  }"
                  @error="handleImageError"
                  @click.stop="showImagePreview(frame, $event)"
                />

                <!-- 关键帧标识 -->
                <div
                  v-if="frame.is_key_frame"
                  class="absolute top-1 left-1 w-2 h-2 bg-yellow-400 rounded-full border border-white"
                  title="关键帧"
                ></div>

                <!-- 时间标签 -->
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs px-1 py-0.5 text-center">
                  {{ formatTime(frame.timestamp) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 播放头 -->
          <div
            v-if="currentTime !== null"
            class="absolute top-0 bottom-0 w-0.5 bg-red-500 pointer-events-none z-20"
            :style="{ left: playheadPosition + 'px' }"
          >
            <div class="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
          </div>
        </div>
      </div>

      <!-- 时间轴控制 -->
      <div class="space-y-3">
        <!-- 时间滑块 -->
        <div class="relative">
          <input
            type="range"
            :min="0"
            :max="videoDuration"
            :step="0.1"
            v-model="currentTime"
            @input="onTimeChange"
            class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div class="flex justify-between text-xs text-gray-500 mt-1">
            <span>{{ formatTime(0) }}</span>
            <span>{{ formatTime(videoDuration) }}</span>
          </div>
        </div>

        <!-- 当前选中帧信息 -->
        <div v-if="selectedFrame" class="bg-white rounded-lg p-3 border">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">
                帧 #{{ selectedFrame.frame_number }}
                <span v-if="selectedFrame.is_key_frame" class="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">关键帧</span>
              </h4>
              <p class="text-xs text-gray-500 mt-1">
                时间: {{ formatTime(selectedFrame.timestamp) }} | 
                尺寸: {{ selectedFrame.width }}x{{ selectedFrame.height }} |
                大小: {{ formatFileSize(selectedFrame.file_size) }}
              </p>
            </div>
            <button
              @click="downloadFrame"
              class="text-sm bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded"
            >
              下载
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 无帧数据 -->
    <div v-else class="text-center text-gray-500 py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
      <p class="text-sm">暂无帧数据</p>
      <p class="text-xs mt-1">请先提取关键帧</p>
    </div>

    <!-- 工具提示 -->
    <div
      v-if="tooltip.show"
      class="fixed z-50 bg-black bg-opacity-75 text-white text-xs rounded px-2 py-1 pointer-events-none"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      {{ tooltip.text }}
    </div>

    <!-- 图片预览模态框 -->
    <div
      v-if="imagePreview.show"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4"
      @click="closeImagePreview"
    >
      <div class="relative max-w-[90vw] max-h-[90vh] flex flex-col">
        <!-- 关闭按钮 -->
        <button
          @click="closeImagePreview"
          class="absolute top-2 right-2 z-10 bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-full p-2 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <!-- 左切换按钮 -->
        <button
          v-if="getCurrentFrameIndex() > 0"
          @click.stop="showPreviousFrame"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-full p-3 transition-colors"
          title="上一帧 (←)"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- 右切换按钮 -->
        <button
          v-if="getCurrentFrameIndex() < frames.length - 1"
          @click.stop="showNextFrame"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-black bg-opacity-50 hover:bg-opacity-75 text-white rounded-full p-3 transition-colors"
          title="下一帧 (→)"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>

        <!-- 预览图片 -->
        <img
          :src="getFrameUrl(imagePreview.frame?.file_path)"
          :alt="`Frame at ${formatTime(imagePreview.frame?.timestamp)}`"
          class="max-w-full max-h-[calc(90vh-8rem)] object-contain rounded-lg shadow-2xl"
          @click.stop
        />

        <!-- 图片信息 -->
        <div class="mt-4 bg-black bg-opacity-75 text-white rounded-lg p-3">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-medium">
                帧 #{{ imagePreview.frame?.frame_number }}
                <span v-if="imagePreview.frame?.is_key_frame" class="ml-2 text-sm bg-yellow-500 text-black px-2 py-0.5 rounded">关键帧</span>
                <span class="ml-2 text-sm text-gray-300">({{ getCurrentFrameIndex() + 1 }}/{{ frames.length }})</span>
              </h4>
              <p class="text-sm text-gray-300 mt-1">
                时间: {{ formatTime(imagePreview.frame?.timestamp) }} |
                尺寸: {{ imagePreview.frame?.width }}x{{ imagePreview.frame?.height }} |
                大小: {{ formatFileSize(imagePreview.frame?.file_size) }}
              </p>
              <p class="text-xs text-gray-400 mt-1">
                使用 ← → 键或点击按钮切换帧，ESC 键关闭
              </p>
            </div>
            <button
              @click="downloadFrameFromPreview"
              class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              下载
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useVideoStore } from '@/stores/videos'

const props = defineProps({
  videoId: {
    type: Number,
    required: true
  },
  videoDuration: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['frame-selected', 'time-changed'])

const videoStore = useVideoStore()

// 响应式数据
const loading = ref(false)
const frames = ref([])
const selectedFrame = ref(null)
const currentTime = ref(0)
const scrollPosition = ref(0)
const frameContainer = ref(null)
const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  text: ''
})
const imagePreview = ref({
  show: false,
  frame: null
})

// 计算属性
const frameWidth = computed(() => {
  if (frames.value.length === 0) return 0
  // 固定帧宽度为80px，确保有足够的显示空间
  return 80
})

const totalWidth = computed(() => {
  return frameWidth.value * frames.value.length
})

const maxScrollPosition = computed(() => {
  if (!frameContainer.value) return 0
  const containerWidth = frameContainer.value.clientWidth
  return Math.max(0, totalWidth.value - containerWidth)
})

const playheadPosition = computed(() => {
  if (props.videoDuration === 0 || frames.value.length === 0) return 0
  const progress = currentTime.value / props.videoDuration
  const absolutePosition = progress * totalWidth.value
  // 播放头位置是绝对位置，不需要减去滚动位置，因为它是相对于整个时间轴容器的
  return absolutePosition
})

// 方法
const loadFrames = async () => {
  try {
    loading.value = true
    const response = await videoStore.getVideoFrames(props.videoId)
    // 处理API响应结构
    frames.value = response.data?.frames || []
  } catch (error) {
    console.error('Failed to load frames:', error)
  } finally {
    loading.value = false
  }
}

const selectFrame = (frame) => {
  selectedFrame.value = frame
  currentTime.value = frame.timestamp
  emit('frame-selected', frame)
  emit('time-changed', frame.timestamp)
}

const onTimeChange = () => {
  // 找到最接近当前时间的帧
  if (frames.value.length > 0) {
    const closestFrame = frames.value.reduce((prev, curr) => {
      return Math.abs(curr.timestamp - currentTime.value) < Math.abs(prev.timestamp - currentTime.value) ? curr : prev
    })
    selectedFrame.value = closestFrame
    emit('frame-selected', closestFrame)
  }
  emit('time-changed', currentTime.value)

  // 自动滚动到播放头位置
  scrollToPlayhead()
}

const scrollLeft = () => {
  if (frameContainer.value) {
    const scrollAmount = frameWidth.value * 3 // 一次滚动3帧的距离
    frameContainer.value.scrollLeft = Math.max(0, frameContainer.value.scrollLeft - scrollAmount)
  }
}

const scrollRight = () => {
  if (frameContainer.value) {
    const scrollAmount = frameWidth.value * 3 // 一次滚动3帧的距离
    frameContainer.value.scrollLeft = Math.min(maxScrollPosition.value, frameContainer.value.scrollLeft + scrollAmount)
  }
}

const onScroll = () => {
  if (frameContainer.value) {
    scrollPosition.value = frameContainer.value.scrollLeft
  }
}

const scrollToPlayhead = () => {
  if (!frameContainer.value || props.videoDuration === 0 || frames.value.length === 0) return

  const progress = currentTime.value / props.videoDuration
  const playheadAbsolutePosition = progress * totalWidth.value
  const containerWidth = frameContainer.value.clientWidth

  // 如果播放头不在可视区域内，则滚动到播放头位置
  const currentScrollLeft = frameContainer.value.scrollLeft
  const visibleStart = currentScrollLeft
  const visibleEnd = currentScrollLeft + containerWidth

  if (playheadAbsolutePosition < visibleStart || playheadAbsolutePosition > visibleEnd) {
    // 将播放头居中显示
    const targetScrollLeft = Math.max(0, playheadAbsolutePosition - containerWidth / 2)
    frameContainer.value.scrollLeft = Math.min(targetScrollLeft, maxScrollPosition.value)
  }
}

const showTooltip = (frame, event) => {
  tooltip.value = {
    show: true,
    x: event.clientX + 10,
    y: event.clientY - 30,
    text: `帧 #${frame.frame_number} - ${formatTime(frame.timestamp)}${frame.is_key_frame ? ' (关键帧)' : ''}`
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}

const getFrameUrl = (filePath) => {
  // 构建帧图片的URL，使用相对路径，通过Vite代理
  return `/api/v1/files/frame/${encodeURIComponent(filePath)}`
}

const handleImageError = (event) => {
  // 图片加载失败时的处理
  event.target.src = '/placeholder-frame.png' // 可以设置一个占位图
}

const downloadFrame = () => {
  if (selectedFrame.value) {
    downloadFrameFile(selectedFrame.value)
  }
}

const downloadFrameFromPreview = () => {
  if (imagePreview.value.frame) {
    downloadFrameFile(imagePreview.value.frame)
  }
}

const downloadFrameFile = (frame) => {
  const link = document.createElement('a')
  link.href = getFrameUrl(frame.file_path)
  // 确保文件名有正确的.jpg后缀
  const timeStr = formatTime(frame.timestamp).replace(/:/g, '-').replace(/\./g, '-')
  const fileName = `frame_${frame.frame_number}_${timeStr}.jpg`
  link.download = fileName
  // 设置target为_blank确保下载行为
  link.target = '_blank'
  // 添加到DOM，点击后移除
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const showImagePreview = (frame, event) => {
  imagePreview.value = {
    show: true,
    frame: frame
  }
  // 阻止事件冒泡，避免触发selectFrame
  if (event) {
    event.stopPropagation()
  }
}

const closeImagePreview = () => {
  imagePreview.value = {
    show: false,
    frame: null
  }
}

// 获取当前预览帧的索引
const getCurrentFrameIndex = () => {
  if (!imagePreview.value.frame) return -1
  return frames.value.findIndex(frame => frame.id === imagePreview.value.frame.id)
}

// 切换到上一帧
const showPreviousFrame = () => {
  const currentIndex = getCurrentFrameIndex()
  if (currentIndex > 0) {
    imagePreview.value.frame = frames.value[currentIndex - 1]
  }
}

// 切换到下一帧
const showNextFrame = () => {
  const currentIndex = getCurrentFrameIndex()
  if (currentIndex < frames.value.length - 1) {
    imagePreview.value.frame = frames.value[currentIndex + 1]
  }
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (!imagePreview.value.show) return

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      showPreviousFrame()
      break
    case 'ArrowRight':
      event.preventDefault()
      showNextFrame()
      break
    case 'Escape':
      event.preventDefault()
      closeImagePreview()
      break
  }
}

// 工具函数
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 100)
  return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// 监听器
watch(() => props.videoId, () => {
  if (props.videoId) {
    loadFrames()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.videoId) {
    loadFrames()
  }
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3B82F6;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}
</style>
